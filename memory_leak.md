# CloudQuery 内存泄漏分析报告

## 1. 问题背景

根据任务描述和现有架构文档，CloudQuery 应用存在一个由 Redux 状态累积导致的内存泄漏。问题的核心在于，`QueryPoolPane.tsx` 组件在获取 SQL 查询结果后，将大量数据存入 Redux store，但由于该组件是全局组件、永不卸载，这些数据缺乏有效的清理机制，导致内存随用户操作持续增长。

本文档旨在深入分析该问题的根本原因、数据流以及相关的代码实现，并明确指出导致泄漏的具体环节。

## 2. 数据流与代码分析

### 2.1. 数据来源与生成

问题的起点位于 [`src/components/queryPoolPane/QueryPoolPane.tsx`](src/components/queryPoolPane/QueryPoolPane.tsx) 的 `moreOperationAfterExecute` 函数。此函数负责处理 SQL 查询轮询返回的结果。

1.  **数据累积**: 在函数内部，`results.current.push(item.response)` 这行代码将每一次查询操作返回的结果 `item.response`（类型为 `QueryResult`）添加到一个 `useRef` 创建的 `results` 数组中。这意味着在单次完整的 SQL 执行（可能包含多次轮询）中，`results.current` 会包含所有的中间和最终结果。

2.  **`resultTabs` 变量的生成**: 在数据处理的末尾（[`QueryPoolPane.tsx:129-132`](src/components/queryPoolPane/QueryPoolPane.tsx:129-132)），代码执行了以下操作：
    ```typescript
    const final = results.current.slice(-20)
    const resultTabs = final.map((info, index) => ({
      key: `execute/${queryTabKey}-${index}`,
      info,
    }))
    ```
    这里的 `results.current` 包含了所有累积的查询结果。代码从中生成 `resultTabs`，这是一个包含了 `key` 和 `info`（即 `QueryResult` 对象）的数组。`info` 字段直接持有查询返回的完整数据，包括列信息和所有行数据，这部分数据量可能非常大。

### 2.2. Redux 存储机制

生成 `resultTabs` 后，代码立即通过 dispatch 两个 action 将数据存入 Redux：

```typescript
// src/components/queryPoolPane/QueryPoolPane.tsx:135-136
dispatch(updateResultTabs(resultTabs))
dispatch(setExecuteKeyList({ key: queryTabKey, list }))
```

1.  **`updateResultTabs(resultTabs)`**: 这个 action 的实现在 [`src/pageTabs/queryPage/resultTabs/resultTabsSlice.ts:96-101`](src/pageTabs/queryPage/resultTabs/resultTabsSlice.ts:96-101)。
    ```typescript
    updateResultTabs(state, action: PayloadAction<ResultTab[]>) {
      const { resultTabMap } = state
      action.payload.forEach(({ key, info }) => {
        resultTabMap[key] = { key, info }
      })
    },
    ```
    此 reducer 遍历传入的 `resultTabs` 数组，并将每一个结果对象 ` { key, info }` 存储到 `state.resultTabMap` 中。`resultTabMap` 是一个以 `resultTabKey` 为键，`ResultTab` 对象（包含完整的查询结果 `info`）为值的巨大对象。**这里的 `info` 就是内存中的主要负载。**

2.  **`setExecuteKeyList({ key: queryTabKey, list })`**: 这个 action 的实现在 [`src/pageTabs/queryPage/resultTabs/resultTabsSlice.ts:84-91`](src/pageTabs/queryPage/resultTabs/resultTabsSlice.ts:84-91)。
    ```typescript
    setExecuteKeyList(
      state,
      action: PayloadAction<{ key: string; list: string[] }>,
    ) {
      const { executeKeyListMap } = state
      const { key, list } = action.payload
      executeKeyListMap[key] = list
    },
    ```
    此 reducer 将当前查询标签页 (`queryTabKey`) 与其对应的所有结果标签页的 key (`list`) 关联起来，并存储在 `state.executeKeyListMap` 中。

通过这两个 action，每次 SQL 查询的详细结果数据都被添加到了全局 Redux store 的 `resultTabs.resultTabMap` 中，并且其索引被记录在 `resultTabs.executeKeyListMap` 中。

## 3. 内存泄漏成因

结合 `architecture.md` 中提到的核心架构事实，内存泄漏的原因变得非常清晰：

1.  **全局组件，状态永生**: [`QueryPoolPane`](src/components/queryPoolPane/QueryPoolPane.tsx) 在 [`src/appPages/main/Main.tsx`](src/appPages/main/Main.tsx) 中被全局渲染，并且永不卸载。这意味着它的生命周期与整个应用的生命周期相同。

2.  **Redux 状态持续累积**: `resultTabMap` 和 `executeKeyListMap` 是 Redux store 的一部分。每次用户执行新的查询，新的结果数据就会被 **添加** 到 `resultTabMap` 中，而不是替换旧的。`resultTabMap` 对象会像滚雪球一样越来越大。

3.  **缺乏有效的清理机制**: 因为 `QueryPoolPane` 组件永不卸载，所以不能依赖组件卸载时执行清理逻辑（如 `useEffect` 的 cleanup 函数）。数据一旦进入 Redux store，除非有明确的 action 来删除它，否则它将永远存在于内存中，直到用户刷新页面。

**结论**: 内存泄漏的根本原因是 **“写入”操作与“清理”操作的严重不对等**。数据被持续地写入全局 Redux store，但代码中缺少在适当时机（如关闭查询标签页、执行新的查询等）清理旧查询结果的逻辑。

## 4. 清理机制排查

通过分析 `resultTabsSlice.ts`，我们可以找到一些与删除相关的 action，但它们并未被有效利用来解决问题。

-   `deleteResultTabsByKeys(state, action: PayloadAction<string[]>)`: 能够根据 key 数组从 `resultTabMap` 中删除数据。
-   `deleteExecuteKeyList(state, action: PayloadAction<string>)`: 能够从 `executeKeyListMap` 中删除指定 `queryTabKey` 的条目。
-   `removeResultTabPane(...)`: 在关闭单个结果标签页时被调用，可以删除一个 result key。
-   `resetResultList`: 这个 thunk action 看起来是清理的核心，它会调用 `deleteResultTabsByKeys`。

然而，关键在于这些清理函数是否在 **关闭整个查询标签页 (Query Tab)** 时被调用。在 `QueryPoolPane.tsx` 中，并没有发现任何在查询标签页关闭时，调用 `resetResultList` 或类似清理函数的逻辑。

`resetResultList` 在 `queryExecuteResults` thunk 的开头被调用，这意味着 **只有当用户在同一个查询标签页中重新执行查询时，上一次的查询结果才会被清理**。如果用户打开了多个查询标签页（例如 Tab A, Tab B, Tab C），然后关闭了 Tab A 和 Tab B，那么 A 和 B 对应的所有查询结果数据依然会残留在 `resultTabMap` 中，因为没有任何机制触发对它们的清理。

因此，可以确认：**当前应用缺少在关闭查询标签页时，清理其关联的所有查询结果数据的机制**，这是导致内存泄漏的直接原因。

## 5. 总结

```mermaid
graph TD
    A[用户执行SQL查询] --> B[QueryPoolPane.tsx: `recursiveQuery` 轮询]
    B --> C[获取查询结果分片]
    C --> D[moreOperationAfterExecute: `results.current.push(result)`]
    D --> E[生成 `resultTabs` (包含完整数据)]
    E --> F[dispatch(`updateResultTabs`, `setExecuteKeyList`)]
    F --> G[Redux Store: `resultTabMap` 和 `executeKeyListMap` 体积增大]
    G --缺乏清理机制--> H((内存泄漏))
    I[关闭查询标签页] --x J[没有调用清理Action]
    H -- "仅在同一标签页再次执行查询时, 才会清理上一次的结果" --> A
```

内存泄漏的根源在于：`QueryPoolPane` 组件将每次查询操作产生的大量结果数据无限地累积到全局 Redux store 的 `resultTabs.resultTabMap` 对象中。由于 `QueryPoolPane` 自身永不卸载，且应用缺乏在“关闭查询标签页”这一关键生命周期事件中触发相应 Redux 状态清理的逻辑，导致内存被无效数据持续占用，无法释放。

[done]