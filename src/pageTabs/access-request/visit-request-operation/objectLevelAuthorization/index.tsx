/**
 * 不同对象级别授权
 */
import React, { useEffect, useMemo, useState } from 'react'
import { ShoppingCartOutlined, PlusOutlined, SearchOutlined } from "@ant-design/icons"
import * as _ from 'lodash';
import { useHistory } from 'react-router-dom';
import type { TableRowSelection } from "antd/es/table/interface"
import { Badge, Button, Table, Checkbox, Tooltip, Space, Input, InputNumber, message, Alert } from "antd"
import { getFlowPermissionsPanelObject, getPermissionsPanelObject } from "src/api"
import { useRequest, useDispatch } from 'src/hook'
import { getScrollX, renderTableFields } from 'src/util'
import { Iconfont } from 'src/components'
import { FineGrainedPermissionModal } from '../components/FineGrainedPermissionModal';
import { updatePermissionCollections } from '../visitRequestOperateSlice'
import { useTranslation } from 'react-i18next';
import styles from "./index.module.scss"
import i18next from 'i18next';
import { setMineApplyPageState, setMineApplyDetailParams } from 'src/pageTabs/access-request/accessRequestSlice'

interface IProps {
  selectTreeItem?: any;
  cartNumber?: boolean;
  needRefresh?: boolean;
  onCancelBatchOperation?: () => void;
  viewAppliedDetail?: boolean; //仅查看
  [p: string]: any
}

const renderGroupName = (nodeName: string) => {
  return nodeName === i18next.t('flow_tables') ? i18next.t('flow_table_name') : nodeName
}

const ObjectLevelAuthorization = ({
  cartNumber,
  selectTreeItem,
  roleId,
  updateParams,
  canEdit = true,
  viewAppliedDetail = false,
  filter,
  needRefresh,
  onCancelBatchOperation,
  isGroupTab,
  canGoToApply = true,
}: IProps) => {
  const { t } = useTranslation();
  const dispatch = useDispatch()
  const [showDefaultPagination, setShowDefaultPagination] = useState(true) // 展示默认的前端分页
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 1,
  });
  
  const [curPagination, setCurPagination] = useState<{pageNo: number, pageSize: number}>({
    pageNo: 1,
    pageSize: 10
  })
  const [columns, setColumns] = useState<any[]>([]);
  const [authorizeList, setAuthorizeList] = useState<any[]>([])
  const [canOperate, setCanOperate] = useState<boolean>(true)
  const [dataSource, setDataSource] = useState<any[]>([])
  const [permissionCollectionVOS, setPermissionCollectionVOS] = useState<any[]>([])
  const [hasPermissionCollectionVOS, setHasPermissionCollectionVOS] = useState<any[]>([])  // 已有权限对象集

  //批量操作schema一下层级
  const [batchOperationState, setBatchOperationState] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [isShowAuthModal, setIsShowAuthModal] = useState(false)
  const [selectedRows, setSelectedRows] = useState([]);
  //筛选条件 后端搜索
  const [searchValue, setSearchValue] = useState<string | undefined>();
  // 查询对象集权限回显
  const { data: objectResult, run: queryPermissionsObject, loading } = useRequest(
    (params) => {
      if (isGroupTab) {
        return getPermissionsPanelObject(params)
      }
      return getFlowPermissionsPanelObject(params)
    },
    {
      manual: true,
      debounceInterval: 300,
      onSuccess:(res, params) => {
        // 非筛选时的请求
        if(!params[0]?.filterNodeName){
          if(showDefaultPagination){
            const data: any = formatData(res);
            const copyData = _.cloneDeep(data);
            copyData?.map((item: any) => {
              item.permissionCollectionObjectVO = {
                ...item.permissionCollectionObjectVO,
                jsonObject: {},  // 默认值不更新参数
                operations: []   // 默认值不回填
              }
              return { ...item }
            })
            setHasPermissionCollectionVOS(data)
            setPermissionCollectionVOS(copyData)
          }else {
            const permissionKeys = permissionCollectionVOS?.map((i: any) => i?.permissionCollectionObjectVO?.nodePathWithType)
            const resData = res?.authObjectVoList?.filter((i: any) => !permissionKeys.includes(i?.nodePathWithType))
            if(resData?.length){
              const data: any = formatData({authObjectVoList: resData});
              const result = [...permissionCollectionVOS, ...data];
              const resultCopy = _.cloneDeep(result);
              resultCopy?.map((item: any) => {
                item.permissionCollectionObjectVO = {
                  ...item.permissionCollectionObjectVO,
                  jsonObject: {},  // 默认值不更新参数
                  operations: []   // 默认值不回填
                }
                return { ...item }
              })
              setHasPermissionCollectionVOS(result)
              setPermissionCollectionVOS(resultCopy)
            }
          }
        }
        if(!showDefaultPagination && params[0]?.pageNo){
          setCurPagination({pageNo: params[0]?.pageNo, pageSize: params[0]?.pageSize})
        }
      }
    }
  );

  const formatData = (res: any) => {

    const { authObjectVoList = [] } = res || {}
    // 初始化选中值
    const initPermissionCollectionVOS = authObjectVoList?.map((item: any) => {
      const { jsonObject, connectionType, nodePathWithType, nodeName, nodePath } = item
      const operations = Object.keys(jsonObject)?.map(i => {
        if (jsonObject[i]) {
          return i
        }
        return false
      })?.filter(i => i)

      if (operations?.length) {
        return {
          permissionCollectionObjectVO: {
            dataSourceType: connectionType,
            nodePathWithType: nodePathWithType,
            nodeName,
            nodePath,
            operations
          }
        }
      }
      return false
    })?.filter((i: any) => i);

    return initPermissionCollectionVOS;
  }

  useEffect(()=>{
    if(selectTreeItem?.sdt?.supportPaging){
      setShowDefaultPagination(false)
    }else{
      setShowDefaultPagination(true)
    }
  },[selectTreeItem?.sdt?.supportPaging])

  useEffect(() => {
    const objectResultCopy = _.cloneDeep(objectResult)
    if (_.isEmpty(objectResultCopy)) return
    setAuthorizeList(objectResultCopy?.headList || [])
    let dataSource: any[] = []
    if(showDefaultPagination){
      setPagination({
        current: 1,
        pageSize: 10,
        total: objectResultCopy?.authObjectVoList?.length
      });
      dataSource = objectResultCopy?.authObjectVoList?.slice(0, 10) || []

    }else {
      dataSource = objectResultCopy?.authObjectVoList ?? [];
    }

    dataSource?.map((item: any) => {
      item.jsonObject = {}   // 默认值不回填
      return { ...item }
    })
    setDataSource(dataSource);
    setCanOperate(canEdit && objectResult?.canOperation)
  }, [objectResult, showDefaultPagination])

  useEffect(() => {
    setSearchValue(undefined);
    setDataSource([]);
    setPermissionCollectionVOS([])
  }, [selectTreeItem?.key])

  useEffect(() => {
    queryDatas();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectTreeItem?.key, needRefresh, searchValue]);

  const queryDatas = (paraments?: any) => {
    if (!selectTreeItem) {
      return;
    }
    const {
      nodeName,
      nodePathWithType,
      nodeType,
      dataSourceType,
      sdt,
    } = selectTreeItem;
    const connectionType = dataSourceType || sdt?.connectionType
    const supportPaging = sdt?.supportPaging
    let params: any = {
      connectionId: null,
      connectionType,
      nodeType,
      nodeName,
      nodePathWithType,
      roleId,
      filter,
      filterNodeName: searchValue
    };
    if(supportPaging){
      // 解决刷新列表数据回到第一页的bug CQ-8729
      params.pageNo = pagination.current
      params.pageSize = pagination.pageSize
    }
    params = { ...params, ...paraments };
    queryPermissionsObject(params)
  }
  useEffect(() => {
    setCanOperate(canEdit)
  }, [canEdit])

  useEffect(() => {
    setColumns((c: any[]) => {
      const arr: any[] = [{
        title: renderGroupName(selectTreeItem?.nodeName),
        dataIndex: "objectName",
        key: "objectName",
        width: 200,
        fixed: "left",
        ellipsis: true,
        render: (txt: string) => <span><Iconfont className={styles.mr4} type={`icon-${selectTreeItem?.nodeType}`} /><Tooltip title={renderTableFields(txt)}>{renderTableFields(txt)}</Tooltip></span>
      },];
      authorizeList?.forEach((item: any) => {
        const itemChecked = !!dataSource?.length && !dataSource?.map((i: any) => i?.jsonObject)?.some((i: any) => !i[item?.operation])
        const indeterminate = !itemChecked && dataSource.map((i: any) => i?.jsonObject)?.some((i: any) => i[item?.operation])
        const titleChildNode = <Checkbox
          disabled={!canOperate || !item.canSelect || !dataSource.length}
          onChange={(e) => handleItemCheck(e.target.checked, item?.operation, dataSource)}
          checked={itemChecked}
          indeterminate={indeterminate}
        >
          {item?.operationName}
        </Checkbox>
        arr.push({
          title: (
            item.canSelect?titleChildNode:
            <Tooltip title={t('flow_other_control_methods')}>{titleChildNode}</Tooltip>
          ),
          dataIndex: item?.operation,
          key: item?.operation,
          width:item?.operationName?.length > 5 ? 200 : 140,
          render: (_: string, record: any, index: number) => {
            const renderChildNode = <Checkbox
              disabled={!canOperate || !record?.canSelect?.[item?.operation] || !record?.canOperation}
              checked={record?.jsonObject?.[item?.operation]}
              onClick={(e) => handleAuhtorizeChange(e, item?.operation, record)}
              >
                {/* 添加已有权限标记 */}
                {
                  hasPermissionCollectionVOS[index]?.permissionCollectionObjectVO?.operations?.some((i: string)=>i===item?.operation) &&
                  <Tooltip title={t('flow_repeat_submit')}>
                    &nbsp;<Iconfont type="icon-quanxianshitu" />
                  </Tooltip>
                }
              </Checkbox>
            return record?.canSelect?.[item?.operation] ? renderChildNode :
              <Tooltip title={t('flow_other_control_methods')}>{renderChildNode}</Tooltip>
          },
        });
      });
      return [...arr]
    });
  }, [authorizeList, canOperate, dataSource, selectTreeItem?.nodeName, selectTreeItem?.nodeType, t])

  useEffect(() => {

    // 更新参数

    const authObjectVoList = objectResult?.authObjectVoList || [];

    const newPermissionCollectionVOS = permissionCollectionVOS?.map(item => {
      const defaultTableInfo = authObjectVoList?.find((table: any) => table?.nodePathWithType === item?.permissionCollectionObjectVO?.nodePathWithType);

      return {
        permissionCollectionObjectVO: {
          ...(item?.permissionCollectionObjectVO || {}),
          jsonObject: defaultTableInfo?.jsonObject || {}
        },
      }
    })
    updateParams?.(newPermissionCollectionVOS);

    //更新store存储数据
    const permissionCollectionVOSClone = _.cloneDeep(permissionCollectionVOS)
    permissionCollectionVOSClone?.map((i: any) => {
      const currentValue = permissionCollectionVOSClone?.filter((i: any) => i?.permissionCollectionObjectVO?.key === selectTreeItem?.key)
      const permissionObj = { [selectTreeItem?.key]: currentValue }
      canEdit && dispatch(updatePermissionCollections(permissionObj))
    })

    //更新table选中状态
    setDataSource((d: any[]) => {
      return d?.map((i: any) => {
        i.jsonObject = {}
        if (permissionCollectionVOS?.map(i => i?.permissionCollectionObjectVO?.nodePathWithType)?.includes(i?.nodePathWithType)) {
          const operations = permissionCollectionVOS?.filter((r: any) => {
            return r?.permissionCollectionObjectVO?.nodePathWithType === i?.nodePathWithType
          })?.map(i => i?.permissionCollectionObjectVO?.operations)?.flat()
          operations?.forEach((element) => {
            if (i.canSelect?.[element]) { //可选时才更新
              i.jsonObject[element] = true;
            }            
          });
        }
        return { ...i }
      })
    })

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(permissionCollectionVOS), pagination])

  // 选择整列
  const handleItemCheck = (checked: boolean, checkboxOperation: any, dataSource: any[]) => {

    let clonePermissionCollectionVOS = _.cloneDeep(permissionCollectionVOS);

    const operations = checked ? [checkboxOperation] : [];
    if (!checked) {
      clonePermissionCollectionVOS = clonePermissionCollectionVOS?.map(item => {
        const itemOperation = item?.permissionCollectionObjectVO?.operations;
        const dsItem = dataSource.find(ds => ds?.nodePathWithType === item?.permissionCollectionObjectVO?.nodePathWithType);

        if (!_.isEmpty(dsItem)) {
          return {
            permissionCollectionObjectVO: {
              ...item?.permissionCollectionObjectVO,
              operations: itemOperation?.filter((o: string) => o !== checkboxOperation)
            }
          }
        }
        return item;

      })?.filter((item) => item?.permissionCollectionObjectVO?.operations?.length);
      setPermissionCollectionVOS(clonePermissionCollectionVOS);
      return
    }

    let curPermissionCollectionVOS = dataSource?.map((i: any) => {
      return {
        permissionCollectionObjectVO: {
          dataSourceType: i?.connectionType,
          nodePathWithType: i?.nodePathWithType,
          nodeName: i?.objectName,
          nodePath: i?.nodePath,
          operations
        }
      }
    })

    //合并
    const newOperatins = mergeOperations([...curPermissionCollectionVOS, ...clonePermissionCollectionVOS])
    setPermissionCollectionVOS(newOperatins)
  }

  // 权限修改
  const handleAuhtorizeChange = (e: any, operation: string, record: any) => {

    const checked = e.target.checked;
    const checkedItem = {
      permissionCollectionObjectVO: {
        dataSourceType: record?.connectionType,
        nodePathWithType: record?.nodePathWithType,
        nodeName: record?.nodeName,
        nodePath: record?.nodePath,
        operations: [operation]
      }
    }

    let clonePermissionCollectionVOS = _.cloneDeep(permissionCollectionVOS);

    const changedItem = clonePermissionCollectionVOS?.find(item => item?.permissionCollectionObjectVO?.nodePathWithType === record?.nodePathWithType);
    if (_.isEmpty(changedItem)) {
      clonePermissionCollectionVOS = clonePermissionCollectionVOS.concat([checkedItem])
    } else {
      clonePermissionCollectionVOS = clonePermissionCollectionVOS.map(item => {
        const { permissionCollectionObjectVO = {} } = item;
        if (permissionCollectionObjectVO?.nodePathWithType === record?.nodePathWithType) {

          let operations = permissionCollectionObjectVO?.operations || [];

          if (checked) {
            operations = operations?.concat([operation])
          } else {
            operations = operations.filter((o: any) => o !== operation)
          }
          return {
            permissionCollectionObjectVO: {
              ...permissionCollectionObjectVO,
              operations
            }
          }
        }
        return item
      })?.filter((item) => item?.permissionCollectionObjectVO?.operations?.length);
    }

    setPermissionCollectionVOS(clonePermissionCollectionVOS)

  }

  // 页码修改
  const handlePageChange = (page: number, pageSize: number = 10) => {
    const dataSource = objectResult?.authObjectVoList?.slice((page - 1) * pageSize, page * pageSize)
    setPagination((p) => {
      return { ...p, current: page, pageSize };
    });
    setDataSource(dataSource)
  };
  //批量操作
  const mergeOperations = (data: any) => {
    return data?.reduce((acc: any, curr: any) => {
      let existingItem = acc.find((item: any) => item?.permissionCollectionObjectVO?.nodePathWithType === curr?.permissionCollectionObjectVO?.nodePathWithType);
      if (existingItem) {
        existingItem.permissionCollectionObjectVO.operations = existingItem?.permissionCollectionObjectVO?.operations?.concat(curr?.permissionCollectionObjectVO?.operations);
      } else {
        acc.push({
          permissionCollectionObjectVO: {
            nodePathWithType: curr?.permissionCollectionObjectVO?.nodePathWithType,
            dataSourceType: curr?.permissionCollectionObjectVO?.dataSourceType,
            nodeName: curr?.permissionCollectionObjectVO?.nodeName,
            nodePath: curr?.permissionCollectionObjectVO?.nodePath,
            operations: curr?.permissionCollectionObjectVO?.operations
          }
        });
      }
      return acc;
    }, []);
  }

  const onBatchChangePermission = (operations: string[]) => {

    let batchPermissions = selectedRows?.map((item: any) => ({
      permissionCollectionObjectVO: {
        nodePathWithType: item?.nodePathWithType,
        dataSourceType: selectTreeItem?.dataSourceType,
        operations: operations,
        nodeName: item?.nodeName,
        nodePath: item?.nodePath,
      }
    }))
    const clone = _.cloneDeep(permissionCollectionVOS)
    const mergedOperations = mergeOperations([...batchPermissions, ...clone]);

    setPermissionCollectionVOS(mergedOperations);
    //回到批量前状态
    setIsShowAuthModal(false);
    setSelectedRowKeys([]);
    setSelectedRows([]);
    setCanOperate(true);
    setBatchOperationState(false);
  }

  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys: selectedRowKeys,
    onSelectAll(selected, newSelectedRows) {

      const curRowKeys: any = newSelectedRows.map(row => row?.nodePathWithType);
      let cloneSelectedRows: any = _.cloneDeep(selectedRows);
      let cloneSelectedRowKeys = _.cloneDeep(selectedRowKeys);
      if (selected) {
        cloneSelectedRows = cloneSelectedRows.concat(newSelectedRows)

        cloneSelectedRowKeys = cloneSelectedRowKeys.concat(curRowKeys)
      } else {

        const curKeys = dataSource?.map((row: any) => row?.permissionId) || [];
        cloneSelectedRows = cloneSelectedRows.filter((cr: any) => cr && !curKeys.includes(cr?.nodePathWithType));
        cloneSelectedRowKeys = cloneSelectedRowKeys.filter(k => !curKeys.includes(k))

      }
      setSelectedRows(cloneSelectedRows.filter((i: any) => !_.isEmpty(i)));
      setSelectedRowKeys([...new Set(cloneSelectedRowKeys)]);
    },
    onSelect(item: any, selected) {

      let cloneSelectedRows: any = _.cloneDeep(selectedRows);
      let cloneSelectedRowKeys: any = _.cloneDeep(selectedRowKeys);
      if (selected) {

        cloneSelectedRows = cloneSelectedRows.concat([item])
        cloneSelectedRowKeys = cloneSelectedRowKeys.concat([item?.nodePathWithType])
      } else {
        cloneSelectedRows = cloneSelectedRows.filter((cr: any) => cr?.nodePathWithType !== item.nodePathWithType);
        cloneSelectedRowKeys = cloneSelectedRowKeys.filter((k: any) => k !== item.nodePathWithType)
      }
      setSelectedRows(cloneSelectedRows);
      setSelectedRowKeys(cloneSelectedRowKeys);
    },
  };

  const searchTypeName = useMemo(() => {
    let name = selectTreeItem?.nodeName;

    if (name.endsWith(t('flow_groups'))) {
      name = name.slice(0, -1);
    }
    return name;
  }, [selectTreeItem?.nodeName])

  const handlePageNumChange = (pageNo: number|string|undefined) => {
    if(!pageNo){
      return
    }
    // 解决输入页码查询不失去焦点又立即去点击上下页页码，数据重复请求问题
    document.getElementById("pageInputNumber")?.blur(); 
    // 解决刷新列表数据回到第一页的bug CQ-8729
    setPagination({
      ...pagination,
      current: pageNo as number
    })
    queryDatas({pageNo})
  }

  const handlePageNumUpdate = _.debounce((pageNo: number|string|undefined) => {
    const reg = /^[1-9]\d*$/;
    if (!reg.test(pageNo + '')) {
      return message.warning(t('flow_enter_positive_integer'));
    }
    handlePageNumChange(pageNo)
  }, 500)

  const paginationConfig = showDefaultPagination ?
    {
      ...pagination,
      onChange: handlePageChange,
    }
    : false

  const handleRenderToApplication = () => {
    dispatch(setMineApplyPageState('application'))
    dispatch(setMineApplyDetailParams({}))
  }

  const hasDisbaledTableInfo = useMemo(() => {

    const disbaledTable = dataSource?.find(d => !d?.canOperation);
    return disbaledTable
  },[JSON.stringify(dataSource)])
 
  return (
    <>
      <div className={styles.objectLevelWrap}>
        <div className={styles.title}>
          <span> {(viewAppliedDetail) ? t('flow_selected_permissions') : isGroupTab? selectTreeItem?.nodeName || "" : t('flow_select_permissions')} </span>
          
            <Space>
              {
                !viewAppliedDetail &&
                <Button ghost type='primary' onClick={() => {
                  //批量操作前清空schema以上批量操作
                  if (batchOperationState) {
                    onCancelBatchOperation?.();
                    setCanOperate(true)
                  } else {
                    setCanOperate(false)
                  }
                  setBatchOperationState(!batchOperationState);
                }}>
                  {batchOperationState ? t('flow_cancel_batch') : t('flow_bulk_operation')}
                </Button>
              }
              {
                canGoToApply && 
                <Tooltip title={t('flow_resource_count')}>
                  <Badge count={cartNumber} showZero size="small" className="mr20">
                    < Button type="primary" icon={<ShoppingCartOutlined />}
                      onClick={handleRenderToApplication}
                    >{t('flow_request_list')}</Button>
                  </Badge>
                </Tooltip>
              }
            </Space>
        </div>
        <div className={styles.objFilter}>
          <Input
            allowClear
            value={searchValue}
            prefix={<SearchOutlined />}
            className={styles.inputSearch}
            placeholder={`${t('flow_enter')} ${searchTypeName} ${t('flow_search_by_name')}`}
            onChange={(e: any) => {
              setSearchValue(e.target.value);
            }}
          />
        </div>
        {
            !_.isEmpty(hasDisbaledTableInfo) && 
            <Alert type='info' message={hasDisbaledTableInfo?.canOperationMessage} className='mt10 mb10'/>
          }
        <Table
          rowKey={(item) => item.nodePathWithType}
          columns={columns}
          loading={loading}
          dataSource={dataSource}
          rowSelection={batchOperationState ? rowSelection : undefined}
          pagination={paginationConfig}
          scroll={{
            x: getScrollX(columns),
            y: `calc(100vh - 500px)`,
          }}
        />
        {
          // schema支持分页,有超过一页的数据
          !showDefaultPagination &&
          !(curPagination?.pageNo===1 && !dataSource?.length) &&
          !(curPagination?.pageNo === 1 && (curPagination?.pageSize !== dataSource?.length)) &&
          <div className='flexAlignCenterJustifyEnd mtb10'>
            <span className='mr10'>{t('flow_current_page')} {curPagination?.pageNo} {t('flow_page')}</span>
            <Button 
              onClick={()=>handlePageNumChange(curPagination?.pageNo - 1)} 
              disabled={curPagination?.pageNo === 1}
              className='mr10 options'
              size='small'
            >
              {t('flow_pre_page')}
            </Button>  
            <Button 
              onClick={()=>handlePageNumChange(curPagination?.pageNo + 1)} 
              disabled={curPagination?.pageSize !== dataSource?.length}
              className='mr10 options'
              size='small'
            >
              {t('flow_next_page')}
            </Button>
            {t('flow_jump_to')}
            <InputNumber 
              id="pageInputNumber"
              size='small'
              className='ml10 w60'
              min={1} 
              onChange={handlePageNumUpdate}
            />
          </div>
        }
        <div className={styles.batchFooter}>
          {
            batchOperationState && selectedRowKeys?.length ?
              <Button
                icon={<PlusOutlined />} ghost type='primary'
                onClick={() => setIsShowAuthModal(true)}
              >
                {t('flow_select_permissions')}
              </Button>
              : ''
          }
        </div>
      </div>
      {
        isShowAuthModal &&
        <FineGrainedPermissionModal
          permissions={authorizeList}
          onCancel={() => {
            setIsShowAuthModal(false)
          }}
          onOk={(values: any) => {

            onBatchChangePermission(values)
          }}
        />
      }
    </>
  );
};
export default ObjectLevelAuthorization;
