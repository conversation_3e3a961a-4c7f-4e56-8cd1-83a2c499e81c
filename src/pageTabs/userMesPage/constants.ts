import i18n from 'i18next';

type MessageTypeMapping =  'EXPORT' | 'PROCESS' | 'AUDIT_WARNING' | 'ALARM' | 'RESULTSET_EXPORT' | 'AUDIT_EXPORT' | 'USER_EXPORT';


export const MESSAGE_TYPE_MAPPING: {[k  in MessageTypeMapping]: string} = {
  EXPORT: i18n.t('mes.type.exportTask'),
  PROCESS: i18n.t('mes.type.process'),
  AUDIT_WARNING: i18n.t('mes.type.audit_warning'),
  ALARM: i18n.t('mes.type.alarm'),
  RESULTSET_EXPORT: i18n.t('mes.type.exportTask'),
  AUDIT_EXPORT: i18n.t('mes.type.exportTask'),
  USER_EXPORT: i18n.t('mes.type.exportTask')
}