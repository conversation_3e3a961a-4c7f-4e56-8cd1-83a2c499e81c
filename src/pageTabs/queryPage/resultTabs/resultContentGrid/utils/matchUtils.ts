import { indexColId } from 'src/components'

export interface MatchOptions {
  caseSensitive: boolean
  useRegex: boolean
}

export interface MatchPattern {
  pattern: RegExp | string
  options: MatchOptions
}

export interface MatchResult {
  isMatch: boolean
  colId?: string
}

/**
 * 创建匹配模式
 * @param searchText 搜索文本
 * @param options 匹配选项
 * @returns 匹配模式对象，如果创建失败返回 null
 */
export function createMatchPattern(searchText: string, options: MatchOptions): MatchPattern | null {
  if (!searchText.trim()) {
    return null
  }

  const { caseSensitive, useRegex } = options

  try {
    if (useRegex) {
      // 正则表达式来匹配 /pattern/flags 格式
      const regexWithFlagsMatcher = /^\/(.+)\/([gimyus]*)$/
      const match = searchText.match(regexWithFlagsMatcher)

      let pattern: RegExp

      if (match) {
        // 如果匹配成功，match[1] 是模式，match[2] 是标志
        const regexPattern = match[1]
        const regexFlags = match[2]
        // 用户在输入中显式提供了标志，优先使用这些标志
        pattern = new RegExp(regexPattern, regexFlags)
      } else {
        // 如果不匹配 /pattern/flags 格式，则将整个 searchText 视为模式
        // 标志由 options.caseSensitive 控制
        pattern = new RegExp(searchText, caseSensitive ? '' : 'i')
      }
      
      return { pattern, options }
    } else {
      // 字符串模式，用于部分匹配
      const pattern = caseSensitive ? searchText : searchText.toLowerCase()
      return { pattern, options }
    }
  } catch (error) {
    console.error('创建匹配模式失败:', error)
    return null
  }
}

/**
 * 处理单元格值，转换为可比较的字符串
 * @param cellValue 单元格值
 * @param options 匹配选项
 * @returns 处理后的字符串
 */
export function processCellValue(cellValue: any, options: MatchOptions): string {
  if (cellValue === null || cellValue === undefined) {
    return ''
  }

  let cellString = String(cellValue)
  
  // 非正则表达式且不区分大小写时，转换为小写
  if (!options.useRegex && !options.caseSensitive) {
    cellString = cellString.toLowerCase()
  }

  return cellString
}

/**
 * 执行匹配检查
 * @param cellValue 单元格值
 * @param matchPattern 匹配模式
 * @returns 是否匹配
 */
export function executeMatch(cellValue: any, matchPattern: MatchPattern): boolean {
  if (!matchPattern || cellValue === null || cellValue === undefined) {
    return false
  }

  const cellString = processCellValue(cellValue, matchPattern.options)
  
  if (!cellString) {
    return false
  }

  const { pattern } = matchPattern

  if (pattern instanceof RegExp) {
    return pattern.test(cellString)
  } else if (typeof pattern === 'string') {
    // 部分匹配：包含指定文本
    return cellString.includes(pattern)
  }

  return false
}

/**
 * 获取单元格的实际值，处理嵌套数据结构
 * @param cellData 单元格数据
 * @returns 实际的值
 */
function extractCellValue(cellData: any): any {
  if (cellData && typeof cellData === 'object' && 'value' in cellData) {
    return cellData.value
  }
  // 否则返回原始值
  return cellData
}

/**
 * 对行数据进行匹配检查，返回所有匹配的列ID
 * @param rowData 行数据
 * @param matchPattern 匹配模式
 * @param orderedColIds 可选的有序列ID数组，如果提供则按此顺序遍历
 * @returns 匹配的列ID数组
 */
export function performRowMatching(rowData: any, matchPattern: MatchPattern, orderedColIds?: string[]): string[] {
  if (!rowData || !matchPattern) {
    return []
  }

  const matchedColIds: string[] = []
  
  // 决定使用哪个列ID列表进行遍历
  const colIdsToCheck = orderedColIds || Object.keys(rowData)
  
  colIdsToCheck.forEach(colId => {
    // 排除序号列
    if (colId === indexColId) return
    
    // 确保该列在行数据中存在
    if (!(colId in rowData)) return

    // 提取实际的单元格值
    const actualValue = extractCellValue(rowData[colId])
    
    if (executeMatch(actualValue, matchPattern)) {
      matchedColIds.push(colId)
    }
  })

  return matchedColIds
}

/**
 * 批量匹配多行数据
 * @param rows 行数据数组
 * @param matchPattern 匹配模式
 * @param orderedColIds 可选的有序列ID数组，如果提供则按此顺序遍历
 * @returns 匹配结果数组，包含行索引和匹配的列ID
 */
export function performBatchMatching(
  rows: any[],
  matchPattern: MatchPattern,
  orderedColIds?: string[]
): Array<{ rowIndex: number; matchedColIds: string[] }> {
  if (!matchPattern) {
    return []
  }

  const results: Array<{ rowIndex: number; matchedColIds: string[] }> = []

  rows.forEach((row, rowIndex) => {
    const matchedColIds = performRowMatching(row, matchPattern, orderedColIds)
    if (matchedColIds.length > 0) {
      results.push({ rowIndex, matchedColIds })
    }
  })

  return results
}