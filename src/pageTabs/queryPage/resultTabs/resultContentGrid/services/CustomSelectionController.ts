import type {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>umn<PERSON><PERSON>,
  Column,
  RowNode,
  CellClickedEvent
} from '@ag-grid-community/core';
import type { SelectionState, SelectionContext, SelectionMode } from '../types/selection';

export class CustomSelectionController {
  private gridApi: GridApi;
  private columnApi: ColumnApi;
  private selectionState: SelectionState;
  private gridContainer: HTMLElement | null = null; // 缓存网格容器
  private isScrollLoading: boolean; // 是否为无限滚动模式

  constructor(gridApi: GridApi, columnApi: ColumnApi, isScrollLoading: boolean = false) {
    this.gridApi = gridApi;
    this.columnApi = columnApi;
    this.isScrollLoading = isScrollLoading;
    // 初始化时获取并缓存容器
    this.initializeGridContainer();
    
    this.selectionState = {
      selectedColumns: new Set(),
      lastClickedColumn: null,
      lastClickedRow: null,
      selectionMode: null,
      selectedCellIds: new Set(),
      lastClickedCellPosition: null,
      shiftSelectionAnchorCell: null,
      dragStartState: null,
      columnHighlightedCellElements: new Map(),
      isSelectAllActive: false
    };
  }

  // 初始化网格容器引用
  private initializeGridContainer(): void {
    // 尝试通过多种方式找到网格容器
    this.gridContainer = this.findGridContainer();
  }

  // 查找网格容器元素
  private findGridContainer(): HTMLElement | null {
    // 方式 1: 通过 ag-Grid 的主题类名查找
    const themeContainers = [
      '.ag-theme-balham',
      '.ag-theme-balham-dark',
      '.ag-theme-alpine',
      '.ag-theme-alpine-dark'
    ];

    for (const themeClass of themeContainers) {
      const container = document.querySelector(themeClass) as HTMLElement;
      if (container) {
        return container;
      }
    }

    // 方式 2: 通过 ag-root-wrapper 查找
    const rootWrapper = document.querySelector('.ag-root-wrapper') as HTMLElement;
    if (rootWrapper) {
      return rootWrapper.closest('.ag-theme-balham, .ag-theme-balham-dark, .ag-theme-alpine, .ag-theme-alpine-dark') as HTMLElement;
    }

    // 方式 3: 如果都找不到，返回 null（会回退到全局查找）
    return null;
  }

  // 获取网格容器元素（如果缓存失效则重新查找）
  private getGridContainer(): HTMLElement | null {
    if (!this.gridContainer || !document.contains(this.gridContainer)) {
      this.gridContainer = this.findGridContainer();
    }
    return this.gridContainer;
  }

  // 核心方法：处理单元格点击
  onCellClicked = (params: CellClickedEvent): void => {
    const { node, event } = params;

    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    
    if (this.isRowNumberColumn(params.column)) {
      if (event && 'shiftKey' in event && 'ctrlKey' in event && 'metaKey' in event) {
        const mouseEvent = event as MouseEvent;
        this.handleRowSelection(node, mouseEvent.shiftKey, mouseEvent.ctrlKey || mouseEvent.metaKey);
      } else {
        this.handleRowSelection(node, false, false);
      }
    } else {
      if (event && 'shiftKey' in event && 'ctrlKey' in event && 'metaKey' in event) {
        const mouseEvent = event as MouseEvent;
        this.handleCellSelection(params, mouseEvent.shiftKey, mouseEvent.ctrlKey || mouseEvent.metaKey);
      } else {
        this.handleCellSelection(params, false, false);
      }
    }
  };

  // 核心方法：处理列头点击
  onHeaderClicked = (column: Column, event?: MouseEvent): void => {
    // 无限滚动模式下禁用列选择功能
    if (this.isScrollLoading) {
      return;
    }
    
    // 阻止默认排序行为
    this.handleColumnSelection(column,
      event?.shiftKey || false,
      event?.ctrlKey || event?.metaKey || false
    );
  };

  // 选择处理
  public handleRowSelection(rowNode: RowNode, isShiftKey: boolean, isCtrlKey: boolean): void {
    // 任何手动行选择都会退出全选模式
    if (this.selectionState.isSelectAllActive) {
      this.selectionState.isSelectAllActive = false;
    }
    
    this.clearOtherSelections('row');
    this.selectionState.selectionMode = 'row';

    if (isShiftKey && this.selectionState.lastClickedRow && this.selectionState.lastClickedRow !== rowNode) {
      this.selectRowRange(this.selectionState.lastClickedRow, rowNode);
    } else if (isCtrlKey) {
      // Ctrl+点击：切换当前行的选中状态
      rowNode.setSelected(!rowNode.isSelected());
    } else {
      // 单选行：清除其他选择，并选中当前行
      const isCurrentlySelected = rowNode.isSelected();
      const selectedNodes = this.gridApi.getSelectedNodes();
      
      if (selectedNodes.length > 1 || !isCurrentlySelected) {
        this.gridApi.deselectAll();
        rowNode.setSelected(true);
      } else if (selectedNodes.length === 1 && isCurrentlySelected) {
        // 如果只选中了当前行，并且再次点击，则确保它仍然是唯一选中的
        this.gridApi.deselectAll();
        rowNode.setSelected(true);
      }
    }
    
    this.selectionState.lastClickedRow = rowNode;
  }

  private handleColumnSelection(column: Column, isShiftKey: boolean, isCtrlKey: boolean): void {
    // 无限滚动模式下禁用列选择功能
    if (this.isScrollLoading) {
      return;
    }

    // 任何手动列选择都会退出全选模式
    if (this.selectionState.isSelectAllActive) {
      this.selectionState.isSelectAllActive = false;
    }

    this.clearOtherSelections('column');
    this.selectionState.selectionMode = 'column';

    const colId = column.getColId();

    if (isShiftKey && this.selectionState.lastClickedColumn) {
      // Shift + 点击：进行范围选择
      // 如果不是同时按下了 Ctrl 键 (Ctrl+Shift 通常用于"添加到当前选择")
      // 则清除之前的列选择，以实现标准的 Shift 范围选择行为（替换旧选择）
      if (!isCtrlKey) {
        this.selectionState.selectedColumns.clear();
      }
      this.selectColumnRange(this.selectionState.lastClickedColumn, column);
    } else if (isCtrlKey) {
      // Ctrl+点击：多选列
      if (this.selectionState.selectedColumns.has(colId)) {
        this.selectionState.selectedColumns.delete(colId);
      } else {
        this.selectionState.selectedColumns.add(colId);
      }
    } else {
      // 单选列：清除其他选择
      this.selectionState.selectedColumns.clear();
      this.selectionState.selectedColumns.add(colId);
    }

    this.selectionState.lastClickedColumn = column;
    this.updateColumnSelection();
    
    // 在列选择后设置焦点单元格，确保键盘事件能够正常工作
    // 选择第一行的选中列作为焦点单元格
    const firstSelectedColumnId = Array.from(this.selectionState.selectedColumns)[0];
    if (firstSelectedColumnId) {
      this.gridApi.setFocusedCell(0, firstSelectedColumnId);
    }

  }

  private clearOtherSelections(keepMode: SelectionMode): void {
    if (keepMode !== 'row') {
      this.gridApi.deselectAll();
      this.gridApi.clearFocusedCell(); // 清除单元格焦点，解决行号列焦点样式残留问题
    }
    if (keepMode !== 'column' && !this.isScrollLoading) {
      // 无限滚动模式下不处理列选择清理，因为列选择功能已被禁用
      this.selectionState.selectedColumns.clear();
      // 直接清除列高亮DOM引用Map，避免调用clearAllColumnHighlights造成性能损失
      this.selectionState.columnHighlightedCellElements.forEach((element, key) => {
        element.classList.remove(key.startsWith('header_') ? 'ag-header-selected-custom' : 'ag-column-selected-custom');
      });
      this.selectionState.columnHighlightedCellElements.clear();
    }
    if (keepMode !== 'cell') {
      this.selectionState.selectedCellIds.clear();
      this.selectionState.lastClickedCellPosition = null;
      this.selectionState.shiftSelectionAnchorCell = null; // 清除Shift选择锚点
      this.selectionState.dragStartState = null;
      // 单元格高亮现在通过 cellClassRules 处理，需要触发重新评估来清除高亮
      this.gridApi.refreshCells();
    }
  }


  private handleCellSelection(params: CellClickedEvent, isShiftKey: boolean, isCtrlKey: boolean): void {
    this.handleCellClick(params, isShiftKey, isCtrlKey);
  }
  // 手动单元格点击处理
  private handleCellClick(params: CellClickedEvent, isShiftKey: boolean, isCtrlKey: boolean): void {
    const { rowIndex, column } = params;
    if (rowIndex === null || rowIndex === undefined || !column) return; // 无效点击

    // 如果正在拖拽，忽略点击事件（避免与拖拽操作冲突）
    if (this.selectionState.dragStartState?.dragging && params.event?.type === 'click') {
        return;
    }

    // 任何手动单元格选择都会退出全选模式
    if (this.selectionState.isSelectAllActive) {
      this.selectionState.isSelectAllActive = false;
    }

    const colId = column.getColId();
    const cellId = this.getCellId(rowIndex, colId);

    // 如果从其他选择模式切换到单元格选择模式，清除其他模式的选择
    if (this.selectionState.selectionMode !== 'cell') {
      this.clearOtherSelections('cell');
    }
    this.selectionState.selectionMode = 'cell';

    if (isCtrlKey) {
      if (this.selectionState.selectedCellIds.has(cellId)) {
        this.selectionState.selectedCellIds.delete(cellId);
      } else {
        this.selectionState.selectedCellIds.add(cellId);
      }
      this.selectionState.lastClickedCellPosition = { rowIndex, colId };
    } else if (isShiftKey && this.selectionState.shiftSelectionAnchorCell) {
      const currentCellPos = { rowIndex, colId };
      const rangeCellIds = this.getCellsInRectangularRange(
        this.selectionState.shiftSelectionAnchorCell, // 使用专门的Shift锚点
        currentCellPos
      );
      this.selectionState.selectedCellIds.clear();
      rangeCellIds.forEach(id => this.selectionState.selectedCellIds.add(id));
      this.selectionState.lastClickedCellPosition = { rowIndex, colId };
    } else if (isShiftKey && !this.selectionState.shiftSelectionAnchorCell) {
      this.selectionState.selectedCellIds.clear();
      this.selectionState.selectedCellIds.add(cellId);
      this.selectionState.lastClickedCellPosition = { rowIndex, colId };
      this.selectionState.shiftSelectionAnchorCell = { rowIndex, colId };
    } else {
      // 普通单击
      this.selectionState.selectedCellIds.clear();
      this.selectionState.selectedCellIds.add(cellId);
      this.selectionState.lastClickedCellPosition = { rowIndex, colId };
      this.selectionState.shiftSelectionAnchorCell = { rowIndex, colId };

      this.gridApi.setFocusedCell(rowIndex, colId);
    }

    this.gridApi.refreshCells()
  }

  // 范围选择
  private selectRowRange(startRowNode: RowNode, endRowNode: RowNode): void {
    const startIndex = startRowNode.rowIndex!;
    const endIndex = endRowNode.rowIndex!;
    const minIndex = Math.min(startIndex, endIndex);
    const maxIndex = Math.max(startIndex, endIndex);

    // 先清除所有选择
    this.gridApi.deselectAll();

    // 选择范围内的所有行
    for (let i = minIndex; i <= maxIndex; i++) {
      const rowNodeAtIndex = this.gridApi.getDisplayedRowAtIndex(i);
      if (rowNodeAtIndex) {
        rowNodeAtIndex.setSelected(true);
      }
    }
  }
  private selectColumnRange(startColumn: Column, endColumn: Column): void {
    const allColumns = this.columnApi.getAllColumns();
    if (!allColumns) return;
    
    const startIndex = allColumns.findIndex(col => col.getColId() === startColumn.getColId());
    const endIndex = allColumns.findIndex(col => col.getColId() === endColumn.getColId());
    const minIndex = Math.min(startIndex, endIndex);
    const maxIndex = Math.max(startIndex, endIndex);

    // 选择范围内的所有列
    for (let i = minIndex; i <= maxIndex; i++) {
      this.selectionState.selectedColumns.add(allColumns[i].getColId());
    }

    this.updateColumnSelection();
  }

  // 更新列选择的视觉效果 (使用自定义CSS类)
  private updateColumnSelection(): void {
    // 1. 先清除所有列的自定义高亮
    this.clearAllColumnHighlights();

    // 2. 为当前选中的列添加高亮
    this.selectionState.selectedColumns.forEach(colId => {
      const column = this.columnApi.getColumn(colId);
      if (column) {
        this.addColumnHighlight(column);
      }
    });
  }

  // 清除所有列的自定义高亮
  private clearAllColumnHighlights(): void {
    // 使用存储的DOM引用快速清除所有列高亮
    this.selectionState.columnHighlightedCellElements.forEach((element, key) => {
      element.classList.remove(key.startsWith('header_') ? 'ag-header-selected-custom' : 'ag-column-selected-custom');
    });
    this.selectionState.columnHighlightedCellElements.clear();
  }

  // 为指定列添加高亮
  private addColumnHighlight(column: Column): void {
    const colId = column.getColId();
    const gridContainer = this.getGridContainer();
    
    // 在特定的 grid 容器内查找列头，如果没有容器则回退到全局查找
    const headerElement = gridContainer
      ? gridContainer.querySelector(`[col-id="${colId}"]`) as HTMLElement
      : document.querySelector(`[col-id="${colId}"]`) as HTMLElement;
      
    if (headerElement) {
      headerElement.classList.add('ag-header-selected-custom');
      // 存储列头DOM元素引用
      const headerKey = `header_${colId}`;
      this.selectionState.columnHighlightedCellElements.set(headerKey, headerElement);
    }
  }

  // 单元格范围选择 拖拽处理方法
  public onTableMouseDown(event: MouseEvent, gridCell: {rowIndex: number, colId: string, node: RowNode, column: Column} | null): boolean {
    if (!gridCell) return false;
    
    // 任何拖拽选择都会退出全选模式
    if (this.selectionState.isSelectAllActive) {
      this.selectionState.isSelectAllActive = false;
    }
    
    this.clearOtherSelections('cell');
    this.selectionState.selectionMode = 'cell';
    
    // selectedCellIds 的修改将由 handleCellClick 或 onTableMouseMove 处理
    // 确保 dragStartState 被正确设置
    this.selectionState.dragStartState = {
      startRowIndex: gridCell.rowIndex,
      startColId: gridCell.colId,
      dragging: true
    };
    
    // 拖拽开始时，只更新 lastClickedCellPosition，不更新 shiftSelectionAnchorCell
    this.selectionState.lastClickedCellPosition = { rowIndex: gridCell.rowIndex, colId: gridCell.colId };
    event.preventDefault(); // 阻止默认的文本选择等行为
    return true;
  }
  public onTableMouseMove(currentGridCell: {rowIndex: number, colId: string} | null): void {
    if (!this.selectionState.dragStartState?.dragging) return;

    if (currentGridCell) {
      const { startRowIndex, startColId } = this.selectionState.dragStartState;
      const rangeCellIds = this.getCellsInRectangularRange(
        { rowIndex: startRowIndex, colId: startColId },
        { rowIndex: currentGridCell.rowIndex, colId: currentGridCell.colId }
      );
      
      const newSelectedCellIds = new Set(rangeCellIds);
      if (!this.areSetsEqual(this.selectionState.selectedCellIds, newSelectedCellIds)) {
          this.selectionState.selectedCellIds = newSelectedCellIds;
      }
    }
    // 如果 currentGridCell 为 null (鼠标移出表格有效单元格区域)，保持上一次的有效选择
  }
  public onTableMouseUp(): void {
    if (this.selectionState.dragStartState?.dragging) {
      const { startRowIndex, startColId } = this.selectionState.dragStartState;

      let wasActualDrag = false;
      // 检查选择是否从 onTableMouseDown 中设置的初始单个单元格发生了变化
      if (this.selectionState.selectedCellIds.size > 1) {
        wasActualDrag = true;
      }

      if (wasActualDrag) {
        // 如果是实际的拖拽操作，则下一次 Shift 点击的锚点
        // 应该是本次拖拽的起始点。
        this.selectionState.shiftSelectionAnchorCell = {
          rowIndex: startRowIndex,
          colId: startColId
        };
      }
      
      // 清除拖拽状态
      this.selectionState.dragStartState = null;
    }
  }

  // 获取当前选择状态
  getCurrentSelectionContext(): SelectionContext {
    const selectedRows = this.gridApi.getSelectedNodes();
    
    // 如果处于全选模式，提供优化的选择上下文
    if (this.selectionState.isSelectAllActive) {
      // 在全选模式下，我们不需要遍历所有行来计算数量，可以使用更高效的方法
      let totalRowCount = 0;
      this.gridApi.forEachNode(() => totalRowCount++);
      
      return {
        selectionMode: this.selectionState.selectionMode,
        hasSelectedRows: true,
        hasSelectedColumns: false,
        hasSelectedCells: false,
        selectedRowCount: totalRowCount,
        selectedColumnCount: 0,
        cellRangeCount: 0,
        
        // 详细信息
        selectedRows: selectedRows, // AG-Grid 的选中行
        selectedColumns: [],
        cellRanges: [],
        
        // 判断是否为单个单元格
        isSingleCell: false,
        
        // 判断单元格选择是否为规则矩形区域
        isRegularCellSelection: false,
        
        // 全选状态标志
        isSelectAllActive: true
      };
    }
    
    return {
      selectionMode: this.selectionState.selectionMode,
      hasSelectedRows: selectedRows.length > 0,
      hasSelectedColumns: this.selectionState.selectedColumns.size > 0,
      hasSelectedCells: this.selectionState.selectedCellIds.size > 0,
      selectedRowCount: selectedRows.length,
      selectedColumnCount: this.selectionState.selectedColumns.size,
      cellRangeCount: this.selectionState.selectedCellIds.size,
      
      // 详细信息
      selectedRows: selectedRows,
      selectedColumns: Array.from(this.selectionState.selectedColumns),
      cellRanges: [], // 设置为空数组，因为我们不依赖企业版的 CellRange[] 结构
      
      // 判断是否为单个单元格
      isSingleCell: this.selectionState.selectedCellIds.size === 1,
      
      // 判断单元格选择是否为规则矩形区域
      isRegularCellSelection: this.isRegularCellSelection(),
      
      // 全选状态标志
      isSelectAllActive: false
    };
  }
  // 全选所有行
  public selectAllRows(): void {
    this.clearAllSelections(); // 清理之前的任何选择状态
    this.selectionState.isSelectAllActive = true;
    this.selectionState.selectionMode = 'column'; // 改为 'column' 以便与列选择逻辑统一
    
    // 获取所有可见列并记录到 selectedColumns 中
    const allDisplayedColumns = this.columnApi.getAllColumns();
    if (allDisplayedColumns) {
      this.selectionState.selectedColumns = new Set(
        allDisplayedColumns.map(col => col.getColId())
      );
    }
    
    // 使用 refreshCells 强制重新评估 cellClassRules
    this.gridApi.refreshCells();

  }

  // 清除所有选择
  clearAllSelections(): void {
    this.gridApi.deselectAll();
    // clearRangeSelection 是企业版功能，在 Community 版本中不调用
    // this.gridApi.clearRangeSelection();
    this.selectionState.selectedColumns.clear();
    // 直接清除列高亮DOM引用Map，避免调用clearAllColumnHighlights造成性能损失
    this.selectionState.columnHighlightedCellElements.forEach((element, key) => {
      element.classList.remove(key.startsWith('header_') ? 'ag-header-selected-custom' : 'ag-column-selected-custom');
    });
    this.selectionState.columnHighlightedCellElements.clear();
    this.selectionState.selectedCellIds.clear();
    // 单元格高亮现在通过 cellClassRules 处理，移除直接 DOM 操作
    this.selectionState.lastClickedColumn = null;
    this.selectionState.lastClickedRow = null;
    this.selectionState.lastClickedCellPosition = null;
    this.selectionState.shiftSelectionAnchorCell = null; // 清除Shift选择锚点
    this.selectionState.dragStartState = null;
    this.selectionState.selectionMode = null;
    this.selectionState.isSelectAllActive = false; // 重置全选标志
  }

  private getCellId(rowIndex: number, colId: string): string {
    return `${rowIndex}_${colId}`;
  }
  public parseCellId(cellId: string): { rowIndex: number; colId: string } | null {
    const firstUnderscoreIndex = cellId.indexOf('_');
    if (firstUnderscoreIndex === -1) {
      return null;
    }
    
    const rowPart = cellId.substring(0, firstUnderscoreIndex);
    const colPart = cellId.substring(firstUnderscoreIndex + 1);
    
    if (!isNaN(parseInt(rowPart, 10)) && colPart.length > 0) {
      return { rowIndex: parseInt(rowPart, 10), colId: colPart };
    }
    return null;
  }
  // 计算矩形范围内的所有单元格
  private getCellsInRectangularRange(
    startPos: { rowIndex: number; colId: string }, // 明确使用传入的 startPos
    endPos: { rowIndex: number; colId: string }
  ): string[] {
    const cells: string[] = [];
    const allDisplayedColumns = this.columnApi.getAllDisplayedColumns();
    const colStartIndex = allDisplayedColumns.findIndex(c => c.getColId() === startPos.colId); // 明确使用传入的 startPos
    const colEndIndex = allDisplayedColumns.findIndex(c => c.getColId() === endPos.colId); // 明确使用传入的 endPos

    if (colStartIndex === -1 || colEndIndex === -1) return []; // 列无效

    const minRow = Math.min(startPos.rowIndex, endPos.rowIndex); // 明确使用传入的 startPos 和 endPos
    const maxRow = Math.max(startPos.rowIndex, endPos.rowIndex); // 明确使用传入的 startPos 和 endPos
    const minColIdx = Math.min(colStartIndex, colEndIndex);
    const maxColIdx = Math.max(colStartIndex, colEndIndex);

    for (let r = minRow; r <= maxRow; r++) {
      // 确保行存在且可见
      const rowNode = this.gridApi.getDisplayedRowAtIndex(r);
      if (rowNode) {
        for (let c = minColIdx; c <= maxColIdx; c++) {
          cells.push(this.getCellId(r, allDisplayedColumns[c].getColId()));
        }
      }
    }
    return cells;
  }

// 判断当前单元格选择是否为规则矩形区域
private isRegularCellSelection(): boolean {
  if (this.selectionState.selectionMode !== 'cell' || this.selectionState.selectedCellIds.size === 0) {
    return true;
  }

  if (this.selectionState.selectedCellIds.size === 1) {
    return true;
  }

  // 将所有选中的单元格ID解析为行列坐标
  const cellPositions: Array<{ rowIndex: number; colIndex: number }> = [];
  const allDisplayedColumns = this.columnApi.getAllDisplayedColumns();
  
  for (const cellId of this.selectionState.selectedCellIds) {
    const cellInfo = this.parseCellId(cellId);
    if (!cellInfo) {
      continue;
    }
    
    const colIndex = allDisplayedColumns.findIndex(col => col.getColId() === cellInfo.colId);
    
    if (colIndex === -1) {
      continue;
    }
    
    cellPositions.push({
      rowIndex: cellInfo.rowIndex,
      colIndex: colIndex
    });
  }

  if (cellPositions.length === 0) {
    return false;
  }

  // 获取边界值
  const minRow = Math.min(...cellPositions.map(p => p.rowIndex));
  const maxRow = Math.max(...cellPositions.map(p => p.rowIndex));
  const minCol = Math.min(...cellPositions.map(p => p.colIndex));
  const maxCol = Math.max(...cellPositions.map(p => p.colIndex));
  
  // 计算期望的单元格数量（完整矩形）
  const expectedCellCount = (maxRow - minRow + 1) * (maxCol - minCol + 1);
  const actualCount = cellPositions.length;
  
  const result = actualCount === expectedCellCount;
  return result;
}


  // 获取选择的数据
  getSelectedData(): unknown {
    const context = this.getCurrentSelectionContext();
    
    switch (context.selectionMode) {
      case 'row':
        return this.gridApi.getSelectedRows();
      case 'column':
        return this.getSelectedColumnData();
      case 'cell':
        return this.getSelectedManualCellData();
      default:
        return null;
    }
  }

  // 比较两个 Set 是否相等（用于性能优化）
  private areSetsEqual<T>(setA: Set<T>, setB: Set<T>): boolean {
    if (setA.size !== setB.size) return false;
    for (const item of setA) {
      if (!setB.has(item)) return false;
    }
    return true;
  }

  // 获取选中的列数据
  private getSelectedColumnData(): unknown[][] {
    const data: unknown[][] = [];
    
    this.selectionState.selectedColumns.forEach(colId => {
      const columnData: unknown[] = [];
      
      this.gridApi.forEachNode((rowNode) => {
        const value = this.gridApi.getValue(colId, rowNode);
        columnData.push(value || '');
      });
      
      data.push(columnData);
    });

    return data;
  }

  // 获取手动选中单元格的数据（基于selectedCellIds）
  private getSelectedManualCellData(): unknown[][] {
    const cellDataList: unknown[][] = [];
    
    // 确保选中的单元格集合存在且不为空
    if (!this.selectionState?.selectedCellIds?.size) {
      return cellDataList;
    }
    
    // 遍历所有选中的单元格ID
    for (const cellId of this.selectionState.selectedCellIds) {
      const cellValue = this.extractCellValue(cellId);
      if (cellValue !== null && cellValue !== undefined) {
        cellDataList.push([cellValue]);
      }
    }
    
    return cellDataList;
  }

  // 提取单个单元格的值
  private extractCellValue(cellId: string): unknown | null {
    // 确保cellId有效
    if (!cellId || typeof cellId !== 'string') {
      return null;
    }

    // 解析单元格ID获取行列信息
    const cellPosition = this.parseCellId(cellId);
    if (!cellPosition?.rowIndex && cellPosition?.rowIndex !== 0) {
      return null;
    }
    if (!cellPosition?.colId) {
      return null;
    }

    // 获取行节点（使用可选链确保API存在）
    const rowNode = this.gridApi?.getDisplayedRowAtIndex?.(cellPosition.rowIndex);
    if (!rowNode) {
      return null;
    }

    // 获取列定义（使用可选链确保API存在）
    const column = this.columnApi?.getColumn?.(cellPosition.colId);
    if (!column) {
      return null;
    }

    // 提取单元格值（使用可选链确保API存在）
    try {
      return this.gridApi?.getValue?.(column, rowNode) ?? null;
    } catch (error) {
      console.warn(`Failed to get cell value for ${cellId}:`, error);
      return null;
    }
  }

  // 获取选择状态，供 cellClassRules 使用
  public getSelectionState(): SelectionState {
    return this.selectionState;
  }

  // 检查单元格是否被选中（用于 cellClassRules）
  public isCellSelected(rowIndex: number, colId: string): boolean {
    const cellId = this.getCellId(rowIndex, colId);
    return this.selectionState.selectedCellIds.has(cellId);
  }

  // 检查列是否被选中（用于 cellClassRules）
  public isColumnSelected(colId: string): boolean {
    return this.selectionState.selectedColumns.has(colId);
  }

  // 检查指定的单元格、行或列是否在当前选区内
  public isCellInSelection(params: { rowIndex?: number; column?: any; node?: any }): boolean {
    const { rowIndex, column, node } = params;
    
    // 如果处于全选模式，所有行都被选中
    if (this.selectionState.isSelectAllActive) {
      return true;
    }
    
    // 如果没有任何选区，返回 false
    if (!this.selectionState.selectionMode) {
      return false;
    }

    // 检查列选区 - 优先检查列选择，因为列选择应该覆盖该列的所有单元格
    if (this.selectionState.selectionMode === 'column' && column) {
      const colId = column.getColId();
      const isColumnSelected = this.selectionState.selectedColumns.has(colId);
      return isColumnSelected;
    }

    // 检查行选区
    if (this.selectionState.selectionMode === 'row' && node) {
      return node.isSelected();
    }

    // 检查单元格选区
    if (this.selectionState.selectionMode === 'cell' && column && (rowIndex !== null && rowIndex !== undefined)) {
      const colId = column.getColId();
      const cellId = this.getCellId(rowIndex, colId);
      return this.selectionState.selectedCellIds.has(cellId);
    }

    return false;
  }

  // 检查是否处于全选状态（公开方法）
  public isSelectAllActive(): boolean {
    return this.selectionState.isSelectAllActive;
  }

  // 直接选择单元格的公开方法（不需要完整的事件对象）
  public selectCell(rowIndex: number, colId: string): void {
    const cellId = this.getCellId(rowIndex, colId);
    
    // 任何手动单元格选择都会退出全选模式
    if (this.selectionState.isSelectAllActive) {
      this.selectionState.isSelectAllActive = false;
    }

    // 如果从其他选择模式切换到单元格选择模式，清除其他模式的选择
    if (this.selectionState.selectionMode !== 'cell') {
      this.clearOtherSelections('cell');
    }
    this.selectionState.selectionMode = 'cell';

    // 普通单选：清除其他选择，只选中当前单元格
    this.selectionState.selectedCellIds.clear();
    this.selectionState.selectedCellIds.add(cellId);
    this.selectionState.lastClickedCellPosition = { rowIndex, colId };
    this.selectionState.shiftSelectionAnchorCell = { rowIndex, colId };

    // 设置焦点到该单元格
    this.gridApi.setFocusedCell(rowIndex, colId);
    
    // 刷新单元格显示
    this.gridApi.refreshCells();
  }

  // 判断是否点击行号列（公开方法，供 ContextMenuHandlers 使用）
  public isRowNumberColumn(column: any): boolean {
    if (!column) return false;
    
    const colDef = column.getColDef();
    const colId = column.getColId();
    
    return (
      colId === 'rowNumber' ||
      colDef.cellClass === 'number-cell'
    );
  }
}