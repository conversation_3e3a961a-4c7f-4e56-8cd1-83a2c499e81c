import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Input, Checkbox, Button, Typography } from 'antd';
import { SearchOutlined, CloseOutlined } from '@ant-design/icons';
import { debounce } from 'lodash';
import { useTranslation } from 'react-i18next';
import styles from './grid.module.scss';

// 导入图标
import daxiaoxieIcon from 'src/assets/resultTabsAssets/daxiaoxie.svg';
import zhengzeIcon from 'src/assets/resultTabsAssets/zhengze.svg';
import nextIcon from 'src/assets/resultTabsAssets/next.svg';
import prevIcon from 'src/assets/resultTabsAssets/prev.svg';

const { Text } = Typography;

// 搜索框组件的 Props 接口
export interface SearchBoxProps {
  visible: boolean;
  onClose: () => void;
  onSearch?: (searchText: string, options: SearchOptions) => void;
  onFilter?: (searchText: string, options: SearchOptions) => void;
  onQuickFilter?: (searchText: string) => void;
  totalMatches?: number;
  currentMatch?: number;
  onNavigate?: (direction: 'next' | 'prev') => void;
  setNeedFullUpdate?: (need: boolean) => void;
  needFullUpdateRef?: React.MutableRefObject<boolean>;
}

// 搜索选项接口
export interface SearchOptions {
  caseSensitive: boolean;
  useRegex: boolean;
  useQuickFilter: boolean;
}

// 按钮配置接口
interface ButtonConfig {
  id: string;
  title: string;
  icon: string;
  onClick?: () => void;
}

const SearchBox: React.FC<SearchBoxProps> = ({
  visible,
  onClose,
  onSearch,
  onQuickFilter,
  totalMatches = 0,
  currentMatch = 0,
  onNavigate,
  setNeedFullUpdate,
  needFullUpdateRef
}) => {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState('');
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [useRegex, setUseRegex] = useState(false);
  const [useQuickFilter, setUseQuickFilter] = useState(false);
  const [selectedButtons, setSelectedButtons] = useState<Set<string>>(new Set());
  
  // 创建防抖搜索函数的引用
  const debouncedSearchRef = useRef<ReturnType<typeof debounce> | null>(null);
  // 使用 ref 存储 useQuickFilter 状态
  const useQuickFilterRef = useRef<boolean>(false);

  // 按钮配置
  const buttonConfigs: ButtonConfig[] = [
    {
      id: 'daxiaoxie', 
      title: t('sdo_case_sensitive'),
      icon: daxiaoxieIcon,
      onClick: () => handleButtonClick('daxiaoxie')
    },
    {
      id: 'zhengze',
      title: t('sdo_regex'), 
      icon: zhengzeIcon,
      onClick: () => handleButtonClick('zhengze')
    },
  ];

  // 导航按钮配置
  const navigationConfigs = [
    {
      id: 'prev',
      title: t('sdo_search_previous'),
      icon: prevIcon,
      onClick: () => onNavigate?.('prev'),
      disabled: currentMatch <= 1 || totalMatches === 0
    },
    {
      id: 'next', 
      title: t('sdo_search_next'),
      icon: nextIcon,
      onClick: () => onNavigate?.('next'),
      disabled: currentMatch >= totalMatches || totalMatches === 0
    }
  ];

  // 处理按钮点击
  const handleButtonClick = (buttonId: string) => {
    const newSelected = new Set(selectedButtons);
    if (newSelected.has(buttonId)) {
      newSelected.delete(buttonId);
    } else {
      newSelected.add(buttonId);
    }
    setSelectedButtons(newSelected);
    
    // 根据不同的按钮ID执行对应的功能
    switch (buttonId) {
      case 'daxiaoxie': // 区分大小写
        const newCaseSensitive = !caseSensitive;
        setCaseSensitive(newCaseSensitive);
        // 立即触发搜索
        if (debouncedSearchRef.current) {
          debouncedSearchRef.current.cancel();
        }
        performSearch(searchText, {
          caseSensitive: newCaseSensitive,
          useRegex,
          useQuickFilter,
        });
        break;
        
      case 'zhengze': // 正则表达式
        const newUseRegex = !useRegex;
        setUseRegex(newUseRegex);
        // 立即触发搜索
        if (debouncedSearchRef.current) {
          debouncedSearchRef.current.cancel();
        }
        performSearch(searchText, {
          caseSensitive,
          useRegex: newUseRegex,
          useQuickFilter,
        });
        break;
        
      default:
        break;
    }
  };

  // 当组件显示时聚焦到输入框
  useEffect(() => {
    if (visible) {
      const timer = setTimeout(() => {
        const input = document.getElementById('search-input');
        if (input) {
          input.focus();
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [visible]);

  // 同步按钮状态与对应的state
  useEffect(() => {
    const newSelected = new Set<string>();
    if (caseSensitive) {
      newSelected.add('daxiaoxie');
    }
    if (useRegex) {
      newSelected.add('zhengze');
    }
    setSelectedButtons(newSelected);
    performSearch(searchText, {
      caseSensitive,
      useRegex,
      useQuickFilter,
    });
  }, [caseSensitive, useRegex]);

  // 创建防抖搜索函数
  const performSearch = useCallback((value: string, options: SearchOptions) => {
    if (!onSearch) {
      return;
    }
    onSearch(value, options);
  }, [onSearch]);

  // 创建防抖函数
  useEffect(() => {
    debouncedSearchRef.current = debounce((value: string, options: SearchOptions) => {
      performSearch(value, options);
    }, 1000); // 1000ms 防抖延迟

    return () => {
      // 清理防抖函数
      if (debouncedSearchRef.current) {
        debouncedSearchRef.current.cancel();
      }
    };
  }, [performSearch]);

  // 执行搜索的通用函数
  const executeSearch = useCallback((value: string = searchText) => {
    const searchOptions = {
      caseSensitive,
      useRegex,
      useQuickFilter,
    };
    
    if (debouncedSearchRef.current) {
      debouncedSearchRef.current(value, searchOptions);
    }
  }, [searchText, caseSensitive, useRegex, useQuickFilter]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
    
    // 使用防抖搜索
    executeSearch(value);
  };

  // 处理 Enter 键搜索
  const handlePressEnter = () => {
    // 取消防抖，立即执行搜索
    if (debouncedSearchRef.current) {
      debouncedSearchRef.current.cancel();
    }
    performSearch(searchText, {
      caseSensitive,
      useRegex,
      useQuickFilter,
    });
  };

  // 清空搜索
  const handleClear = () => {
    setSearchText('');
    // 取消防抖，立即执行清空搜索
    if (debouncedSearchRef.current) {
      debouncedSearchRef.current.cancel();
    }
    performSearch('', { caseSensitive, useRegex, useQuickFilter });
  };

  // 处理快速筛选选项变化
  const handleQuickFilterChange = (e: any) => {
    const checked = e.target.checked;
    setUseQuickFilter(checked);
    
    // 检测从选中变为不选中，设置需要全量刷新
    if (useQuickFilterRef.current === true && checked === false) {
      if (needFullUpdateRef) {
        needFullUpdateRef.current = true;
      }
      setNeedFullUpdate?.(true);
    }
    
    // 更新 ref
    useQuickFilterRef.current = checked;
    
    // 立即触发搜索
    if (debouncedSearchRef.current) {
      debouncedSearchRef.current.cancel();
    }
    performSearch(searchText, {
      caseSensitive,
      useRegex,
      useQuickFilter: checked,
    });
  };

  if (!visible) {
    return null;
  }

  return (
    <div className={styles.searchContainer}>
      <div className={styles.searchContent}>
        <Input
          id="search-input"
          placeholder={t('sdo_search_placeholder')}
          value={searchText}
          onChange={handleInputChange}
          onPressEnter={handlePressEnter}
          prefix={<SearchOutlined className={styles.searchBarAntIcon} />}
          suffix={
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              {(totalMatches > 0 || searchText) && (
                <Text 
                  type="secondary" 
                  style={{ fontSize: '12px', whiteSpace: 'nowrap' }}
                  className={styles.searchCounterText}
                >
                  {totalMatches > 0
                    ? `${currentMatch} / ${totalMatches}`
                    : searchText
                      ? t('sdo_no_matches')
                      : ''}
                </Text>
              )}
              {searchText && (
                <Button
                  type="text"
                  size="small"
                  icon={<CloseOutlined className={styles.searchBarAntIcon} />}
                  onClick={handleClear}
                  title={t('sdo_search_clear')}
                />
              )}
            </div>
          }
          style={{ width: '300px' }}
        />
        
        <div className={styles.searchOptions}>

        </div>

        <div className={styles.filterButtons}>
          {buttonConfigs.map((config) => {
            // 根据按钮ID确定是否选中
            let isSelected = selectedButtons.has(config.id);
            if (config.id === 'daxiaoxie') {
              isSelected = caseSensitive;
            } else if (config.id === 'zhengze') {
              isSelected = useRegex;
            }
            
            return (
              <Button
                key={config.id}
                type="text"
                size="small"
                title={config.title}
                className={`${styles.filterButton} ${isSelected ? styles.filterButtonSelected : ''}`}
                onClick={config.onClick}
              >
                <img 
                  src={config.icon} 
                  alt={config.title}
                  className={styles.filterButtonIcon}
                />
              </Button>
            );
          })}
          
          {/* 导航按钮 */}
          {navigationConfigs.map((config) => (
            <Button
              key={config.id}
              type="text"
              size="small"
              title={config.title}
              className={styles.filterButton}
              onClick={config.onClick}
              disabled={config.disabled}
            >
              <img 
                src={config.icon} 
                alt={config.title}
                className={styles.filterButtonIcon}
              />
            </Button>
          ))}
        </div>

        <Checkbox
          checked={useQuickFilter}
          onChange={handleQuickFilterChange}
          className={styles.quickFilterCheckbox}
        >
          {t('sdo_filter_rows')}
        </Checkbox>


        <Button
          type="text"
          size="small"
          onClick={onClose}
          title={t('sdo_close_search')}
          className={styles.closeButton}
        >
          ✕
        </Button>
      </div>
    </div>
  );
};
export default SearchBox;