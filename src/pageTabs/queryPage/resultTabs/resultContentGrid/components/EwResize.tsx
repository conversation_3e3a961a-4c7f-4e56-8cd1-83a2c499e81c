import React, { useState, useEffect } from 'react';
import styles from './index.module.scss'

interface IProps {
  minWidth?: number;
  maxWidth?: number;
  customStyle?: any;
  getModalWidth?: (n: number) => void;
}

const EwResize: React.FC<IProps> = ({ minWidth = 800, maxWidth = 1200, customStyle, getModalWidth }) => {
  const [modalWidth, setModalWidth] = useState(minWidth);
  const [isResizing, setIsResizing] = useState(false);
  const [initialMouseX, setInitialMouseX] = useState(0);
  const [initialWidth, setInitialWidth] = useState(minWidth);

  const startResize = (e: React.MouseEvent<HTMLSpanElement>) => {
    if (e.button === 0 && e.target === document.getElementById("ewResizeSpan")) {
      setIsResizing(true);
      setInitialMouseX(e.clientX);
      setInitialWidth(modalWidth);
    }
  };

  const resize = (e: MouseEvent) => {
    if (isResizing) {
      const newWidth = initialWidth + (e.clientX - initialMouseX);
      if (newWidth > maxWidth) return;
      setModalWidth(newWidth > minWidth ? newWidth : minWidth);
    }
  };

  const stopResize = () => {
    setIsResizing(false);
  };

  useEffect(() => {
    document.addEventListener('mouseup', stopResize);
    document.addEventListener('mousemove', resize);
    return () => {
      document.removeEventListener('mousemove', resize);
      document.removeEventListener('mouseup', stopResize);
    };
  }, [isResizing]);

  useEffect(() => {
    if (getModalWidth) {
      getModalWidth(modalWidth);
    }
  }, [modalWidth, getModalWidth]);

  return (
    <button
      id="ewResizeSpan"
      onMouseDown={startResize}
      className={styles.ewResizeSpanSty}
      style={{
        ...customStyle
      }}
    />
  );
};

export default EwResize;
