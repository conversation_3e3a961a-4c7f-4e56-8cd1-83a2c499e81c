@import 'src/styles/variables';

.paginationContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 0;
  background-color: transparent;
  border: none;
}

.paginationButton {
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  border-radius: 4px !important;
  transition: background-color 0.2s ease !important;
  width: 28px !important;
  height: 28px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  
  &:hover:not(:disabled) {
    background: var(--Color05, #DFE2E7) !important;
  }
  
  &:active:not(:disabled) {
    background: var(--Color05, #DFE2E7) !important;
  }
  
  &:disabled {
    background: transparent !important;
  }
  
  // 确保图标在按钮中居中
  .ant-btn-icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.pageSizeSelector {
  color: #000;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  user-select: none;
  transition: background-color 0.2s ease;
  
  &:not(.disabled) {
    cursor: pointer;
    
    &:hover {
      background: var(--Color05, #DFE2E7);
    }
    
    &:active {
      background: var(--Color05, #DFE2E7);
    }
  }
  
  &.disabled {
    cursor: not-allowed;
  }
}

.countIndicator {
  font-size: 14px;
  margin: 0 4px;
  transition: color 0.2s ease;
  
  &.clickable {
    cursor: pointer;
    text-decoration: none;
    
    &:hover {
      color: #1890ff;
    }
  }
  
  &:not(.clickable) {
    cursor: default;
  }
}

.totalCount {
  font-size: 14px;
  margin: 0 4px;
}
