import React, { memo, useMemo } from "react";
import { Select } from "antd";
import { useDispatch, useSelector } from 'src/hook'
import {
  updateTabsInfo
} from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice'
import { useTranslation } from "react-i18next";

const ResultPagSizeSelector = memo(({
  resultPageSize,
  activeTabKey,
  settingPageSize,
  retriggerSearchAfterPagination
}: {
  resultPageSize: number;
  activeTabKey: string;
  settingPageSize: number;
  retriggerSearchAfterPagination?: () => void;
}) => {

  let DEFAULT_PAGESIZE_OPTIONS = [100, 200, 500, 1000];

  const dispatch = useDispatch()
  const { t } = useTranslation()
  const { tabInfoMap } = useSelector((state) => state.queryTabs);

  const pageSizeOptions: number[] = useMemo(() => {

    if (settingPageSize && !DEFAULT_PAGESIZE_OPTIONS.includes(settingPageSize)) {
      return [settingPageSize].concat(DEFAULT_PAGESIZE_OPTIONS)
    }
    return DEFAULT_PAGESIZE_OPTIONS
  }, [settingPageSize, DEFAULT_PAGESIZE_OPTIONS])

  return (
    <div>
      {t('sdo_res_page_nums')}：
      <Select
        size="small"
        value={resultPageSize}
        options={pageSizeOptions?.map(n => ({ label: n, value: n }))}
        onChange={(val: number) => {
          dispatch(updateTabsInfo({ ...tabInfoMap[activeTabKey], resultPageSize: val }))
          // 页面大小改变后重新触发搜索
          if (retriggerSearchAfterPagination) {
            retriggerSearchAfterPagination()
          }
        }}
      />
    </div>
  )
})

export default ResultPagSizeSelector;