import React, { memo, useContext, useState, useMemo, useCallback, useEffect } from "react";
import { WarningTwoTone } from '@ant-design/icons';
import { Input, Popconfirm, Table, Form, message, Alert, Tooltip, Select } from "antd";
import { <PERSON><PERSON>utton, Iconfont } from "src/components";
import { useRequest, useDispatch } from 'src/hook'
import { setHotKeys } from 'src/store/extraSlice/settingSlice'
import { 
  getShortcutList, 
  customShortcutKey, 
  resetShortcutKeys, 
  getUserShortcutTypes,
  IShortcutType,
  updateUserShortcutType
} from 'src/api';
import { useTranslation } from 'react-i18next';
import { resetHotKeys } from 'src/store/extraSlice/settingSlice'
import ShortcutInput from './ShortcutInput';
import styles from './index.module.scss';

interface EditableCellProps {
  defaultValue: string
  editable: boolean
  children: React.ReactNode | null
  record?: any
  editingKey: string | null
  dataIndex: string;
  onSave: (dataIndex: string, record: any) => void
  onClick: () => void
  onEditKey: (key: string | null) => void
}

const NOT_MODIFY_SHORTCUT_KEYS = ['GotoSpecifiedLine'];
const EditableContext = React.createContext<any | null>(null)

const EditableRow: React.FC<any> = ({ index, ...props }) => {
  const [form] = Form.useForm()
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} key={index} />
      </EditableContext.Provider>
    </Form>
  )
}
const EditableCell = memo(({ record, dataIndex, editable, editingKey, onEditKey, children, onSave, ...restProps }: EditableCellProps) => {

  //add 快捷键校验
  let childNode = children;
  const form = useContext(EditableContext)!
  const { t } = useTranslation();

  const onPreSave = (value?: string) => {
  
    form.validateFields().then(() => {
      if (record?.key !== value) {
        onSave(record.name, value);
      } else {
        onEditKey('')
      }
    })
  }
  const hotKeyValidator = (_: any, val: string) => {

    if (!val) {
      return Promise.resolve()
    }
    if (val && val !== '' && !val.includes('+')) {
      return Promise.reject(t('personal:shortcutValidate'))
    }
    return Promise.resolve()
  }

  if (editable) {
    childNode =
      editable && editingKey === record?.name ? (
        <Form.Item
          name={dataIndex}
          rules={[{ validator: hotKeyValidator }]}
        >
          <ShortcutInput onPressKey={onPreSave} onChange={val => {
            form.setFieldsValue({ [dataIndex]: val })
          }} />
        </Form.Item>
      ) : (
        <div style={{ width: '100%', height: 20 }} onClick={() => {

          if (!editingKey && !NOT_MODIFY_SHORTCUT_KEYS.includes(record?.name)) {
            onEditKey(record.name)
            form.setFieldsValue({ [dataIndex]: record[dataIndex] })
          }
        }}>{childNode}</div>
      )

  }

  return record?.name && NOT_MODIFY_SHORTCUT_KEYS.includes(record.name) ? 
  <Tooltip title={t('personal:shortcutDisabledCustomEdit')} placement={'topLeft'}>
     <td {...restProps}>{childNode}</td>
  </Tooltip>:
  <td {...restProps}>{childNode}</td>
});

export default memo(() => {

  const defaultSearchParams = {
    pageNum: 1,
    pageSize: 1000,
  };

  const SHORTCUT_ICON_MAPPING: any = {
    CQ: 'icon-nomal',
    PLSQL: 'icon-a-PLSQLdeveloper',
    NAVICAT: 'icon-Navicat'
  }
  const dispatch = useDispatch();
  const [value, setValue] = useState<IShortcutType>('CQ');

  const [dataSource, setDataSource] = useState<any>([]);
  const [editingKey, setEditingKey] = useState('');
  const [tableSearchParams, setTableSearchParams] = useState<{ pageNum: number; pageSize: number; keyWord?: string }>(defaultSearchParams)
  const { t } = useTranslation();

  //快捷键类型
  const { data: shortcutKeys, run: runGetUserShortcutTypes } = useRequest(getUserShortcutTypes,
    {
      formatResult: (res) => {
        if (!res) return [];
        return res?.map((item: any) => {
          return {
            label: <> <Iconfont type={SHORTCUT_ICON_MAPPING[item?.code]} className="mr10"/>{item?.desc}</>,
            value: item?.code,
            isSelected: item?.isSelected,
          }
        })
      },
      onSuccess(res) {
        const selectedItem = res?.find((i: { label: string; value: string, isSelected: string }) => i?.isSelected)
        if (selectedItem?.value) {
          setValue(selectedItem.value)
        }
      }
    });
  //更新快捷键类型
  const { run: runUpdateUserShortcutType } = useRequest(updateUserShortcutType, {
    manual: true,
    onSuccess: async () => {
      runGetUserShortcutTypes();
      message.success(t("personal:modificationSuccessful"));
      //默认进入项目已经拿到过最新的快捷键,只需要在更新的时候替换就好
      const res: any = await getShortcutList({ pageNum: 1, pageSize: 1000 });
      runGetShortcutList({...tableSearchParams})
      if (res?.data?.length) {
        const hotkeys = res.data.reduce((pre: any, next: any) => {
          return {
            ...pre,
            [next.name]: next.key,
          }
        }, {})
        dispatch(resetHotKeys({ ...hotkeys }))
      }
    }
  })


  const { loading, refresh, run: runGetShortcutList } = useRequest(getShortcutList, {
    manual: true,
    debounceInterval: 200,
    onSuccess: (res) => {
      if (res) {
        setDataSource(res?.data || []);
        //更新全局快捷键
        if (res?.data?.length) {
          const hotkeys = res.data.reduce((pre: any, next: any) => {
            return {
              ...pre,
              [next.name]: next.key,
            }
          }, {})
          dispatch(setHotKeys({ ...hotkeys }))
        }
      }
    }
  })
  //
  const { loading: customKeyLoading, run: runCustomShortcutKey } = useRequest(customShortcutKey, {
    manual: true,
    onSuccess: () => {
      message.success(t("personal:modificationSuccessful"));
      refresh();
      setEditingKey('')
    }
  })
  //重置快捷键
  const { loading: resetLoading, run: runResetShortcutKeys } = useRequest(resetShortcutKeys, {
    manual: true,
    onSuccess: () => {
      message.success(t("personal:resetSuccessfult"));
      refresh();
    }
  })

  useEffect(() => {
    runGetShortcutList({ ...tableSearchParams })
  }, [tableSearchParams])

  const save = useCallback(async (key: string, value: any) => {
    if (!dataSource?.length) return
    try {
      const newData = [...dataSource];
      const index = newData.findIndex((item) => key === item?.name);
      if (index > -1) {
        const item = newData[index] ?? {};
        runCustomShortcutKey({ shortcuts: [{ ...item, key: value }] });
      }
    } catch (errInfo) {
      console.log('Validate Failed:', errInfo);
    }
  }, [dataSource]);

  const onChange = (val: IShortcutType) => {
    runUpdateUserShortcutType(val);
  }

  const defaultColumns = [
    {
      title: t("personal:action"),
      dataIndex: 'action',
      width: '50%'
    },
    {
      title: t("personal:shortcutKeys"),
      dataIndex: 'key',
      width: '50%',
      type: 'input',
      editable: true,
    },
  ];

  const columns = useMemo(() => {
    return defaultColumns.map((col) => {
      if (!col.editable) {
        return col;
      }
      return {
        ...col,
        onCell: (record: any) => ({
          record,
          defaultValue: record[col.dataIndex],
          editable: col.editable,
          type: col.type,
          editingKey,
          dataIndex: col.dataIndex,
          onSave: save,
          onEditKey: setEditingKey,
        }),
      };
    });
  }, [defaultColumns, editingKey, save]);

  return (
    <div className={styles.shortcutKeyManagement}>
      <Alert
        closable
        banner
        showIcon={true}
        style={{ width: '100%' }}
        className="mb10"
        type="info" 
        message={<div className={styles.alertMessage}>
         {t("personal:predefinedShortcutKeysConflict_pre")}
         <span className="color3357ff">{t('personal:predefinedShortcutKeysConflict_postfix')}</span>
        </div>}
      />

      <div className="flexAlignCenterBetween mb10">
        <Input
          allowClear
          placeholder={t("personal:enterActionShortcutKey")}
          style={{ width: '100%'}}
          onChange={(e: any) => setTableSearchParams({ ...defaultSearchParams, keyWord: e.target.value})}
        />
      </div>
      <div className="mb10">
      <Select
        style={{ width: '40%' }}
        placeholder={t("personal:pleaseSelectPredefinedShortcutKeys")}
        options={shortcutKeys}
        onChange={onChange}
        value={value}
      />
        <Popconfirm
          title={t("personal:resetToDefaultValues")}
          onConfirm={() => {
            setEditingKey('')
            runResetShortcutKeys()
          }}
        >
          <LinkButton>{t("personal:resetToDefault")}</LinkButton>
        </Popconfirm>
      </div>
     
      <Table
        loading={loading || customKeyLoading || resetLoading}
        rowKey='name'
        components={{
          body: {
            row: EditableRow,
            cell: EditableCell,
          }
        }}
        columns={columns}
        dataSource={dataSource}
        scroll={{ y: 200 }}
        pagination={false}
        rowClassName={styles.shortcutRow}
      />
    </div>
  )
})