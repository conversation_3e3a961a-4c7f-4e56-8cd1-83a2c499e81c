import React, { useEffect, useState } from 'react';
import { Input, message } from 'antd';
import styles from './index.module.scss';
import { useTranslation } from 'react-i18next';
const ShortcutInput = ({ value, onChange, onPressKey }: {
  value?: string
  onChange: (keyPressed?: string) => void
  onPressKey?: (keyPressed?: string) => void
}) => {
  const { t } = useTranslation();
  const [inputValue, setInputValue] = useState(value);
  const handleKeyDown = (event: any) => {
    if (event.key === 'Backspace') {
      setInputValue(''); // 清空输入框内容
      return;
    }
    event.preventDefault();
    // 获取按下的键
    let keyPressed = event.key;
    const keyCode = event.code;
    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
    if (keyCode.startsWith('Key')) {
      keyPressed = keyCode.replace('Key', '').toUpperCase();
    } else if (keyCode.startsWith('Digit')) {
      keyPressed = keyCode.replace('Digit', '');
    }
    let modifierKeys = [];

    // 检查修饰键
    if (event.ctrlKey) modifierKeys.push('Ctrl');
    if (event.shiftKey) modifierKeys.push('Shift');
    if (event.altKey) modifierKeys.push('Alt');
    if (event.metaKey) modifierKeys.push('Command'); 
    if (isMac && event.altKey) {
      if(keyCode.startsWith('Key')) {
        keyPressed = event.code.replace('Key', '')
      }else if (keyCode.startsWith('Digit')) {
        keyPressed = event.code.replace('Digit', '')
      }
    }
    
    modifierKeys = [...new Set(modifierKeys)];
  
    // 组合键的字符串
    const fullKey = modifierKeys.length > 0 ? `${modifierKeys.join('+')}+${keyPressed}` : keyPressed;
    // 调用传入的按键处理函数
    setInputValue(fullKey);
  };
;
  useEffect(() => {

    const shortcutInputEle = document.getElementById('shortcutInput');
    if (!shortcutInputEle) return;
    // 添加键盘事件监听
    shortcutInputEle.addEventListener('keydown', handleKeyDown);
    
    // 清理事件监听
    return () => {
      shortcutInputEle.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  useEffect(() => {
    onChange(inputValue)
  }, [inputValue])

  return (
    <Input
      id='shortcutInput'
      value={inputValue}
      autoFocus
      placeholder={t("personal:enterContentAndUseShortcutKeys")}
      className={styles['hidden-cursor']} // 添加自定义类名
      onChange={(e) => {
        if (e.target.value) {
          setInputValue(e.target.value);
        }
        if(!e.target.value ) {
          document.getElementById('shortcutInput')?.focus()
        }
        setInputValue(e.target.value)
      }}
      onBlur={() => {setTimeout(() => onPressKey?.(inputValue))}}
    />
  );
};

export default ShortcutInput;
