import React, { useEffect } from "react";
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import { v4 as uuid } from 'uuid'
import { Statistic, Row, Col } from 'antd';
import { useTranslation } from 'react-i18next';
import { useHistory } from 'react-router-dom'
import { Iconfont } from "src/components";
import classNames from "classnames";
import styles from './index.module.scss';
import { useSelector, useDispatch, useRequest, useConditionalPolling } from "src/hook";
import {
  setUserExecuteLogHistoryParams,
} from 'src/pageTabs/audit/overview/overviewSlice'
import {
  getHomeConnTab,
  getHomeExecuteTab,
  getHomeExecuteFailTab
} from 'src/api'

const StatisticItem = ({
  isCompare = false,
  title,
  iconType,
  value,
  subValue = 0,
  onClick
}: {
  isCompare?: boolean;
  title: string;
  iconType: string;
  value: number | string;
  subValue: number;
  onClick?: any;
}) => {

  const { t } = useTranslation();

  return (
    <div className={classNames(styles.statiscItem, {
      [styles.itemHover]: onClick ? true : false,
    })} onClick={() => onClick?.()}>
      <div className={styles.itemContainer}>
        <Statistic
          title={<div className={styles.statiscTitle}>{title}</div>}
          prefix={<Iconfont type={iconType} className={styles.statiscIcon} />}
          formatter={() =>
            <div className={styles.statisicValue}>
              <div className={styles.value}>{value}</div>
              {
                isCompare ?
                  <div className={styles.desc}>{t('home.overview.desc1', {val: `${subValue}%`})}
                  {
                    subValue > 0 ? <ArrowUpOutlined /> : subValue < 0 ? <ArrowDownOutlined /> : ''
                  }
                  </div>
                  :
                  <div className={styles.desc}>{t('home.overview.desc2', {val: subValue})}</div>
              }
            </div>
          }
        />
      </div>
    </div>
  )
}

export default () => {

  const { t } = useTranslation();
  const history = useHistory();
  const dispatch = useDispatch();
  const { activeKey } = useSelector(state => state.pageTabs);
  const { permissionMenus = [] } = useSelector(state => state.login.userInfo)  //判断有无数据操作菜单
  const hasDataOperateMenu = permissionMenus.some(item => item?.menuType === 'SYSTEM_DATA_OPERATE');

  const { data: connData, run: runGetHomeConnTab, cancel: conTabCancel, refresh: conTabRefresh} = useRequest(getHomeConnTab, { manual: true })
  const { data: sqlExecuteData, run: runGetHomeExecuteTab, cancel: sqlExecuteCancel, refresh: sqlExecuteRefresh } = useRequest(getHomeExecuteTab, { manual: true })
  const { data: sqlExecuteFailData, run: runGetHomeExecuteFailTab, cancel: sqlExecuteFailCancel, refresh: sqlExecuteFailRefresh } = useRequest(getHomeExecuteFailTab, { manual: true })

  useEffect(() => {
    runGetHomeConnTab();
    runGetHomeExecuteTab();
    runGetHomeExecuteFailTab();
  },[])

    //当前页面激活时刷新数据
    useConditionalPolling({
      shouldPoll: (key) => key === '/system_home',
      pollFn: conTabRefresh,
      cancelFn: conTabCancel,
      deps: [activeKey],
      interval: 1000*60
    });

    useConditionalPolling({
      shouldPoll: (key) => key === '/system_home',
      pollFn: sqlExecuteRefresh,
      cancelFn: sqlExecuteCancel,
      deps: [activeKey],
      interval: 1000*60
    });

    useConditionalPolling({
      shouldPoll: (key) => key === '/system_home',
      pollFn: sqlExecuteFailRefresh,
      cancelFn: sqlExecuteFailCancel,
      deps: [activeKey],
      interval: 1000*60
    });

  const onGoDataOpetate = () => {
    history.push('/system_data_operate')
  }

  const onGoAuditMenu = (type: 'total' | 'error') => {
    history.push('/sql-log');
    let params: any = {
      resultFlag: null,
      uuid: uuid() // 防止重复点开同一个页面，不刷新
    };
    if (type === 'error') {
      params.resultFlag = '0';
    }

    dispatch(setUserExecuteLogHistoryParams(params))
  }

  return (
    <div className={styles.overviewStatisCard}>
      <div className={styles.title}>{t('home.overview.title')}</div>
      <div className={styles.content}>
        <Row gutter={[51, 0]} className={styles.row}>
          <Col span={8}>
            <StatisticItem
              title={t('home.overview.datasource')}
              iconType="icon-dbconnct"
              value={connData?.connectionCount || 0}
              subValue={connData?.connPermissionTypeCount || 0}
              {...(hasDataOperateMenu ? { onClick: () => onGoDataOpetate() } : {})}
            />
          </Col>
          <Col span={8}>
            <StatisticItem
              isCompare
              title={t('home.overview.executeTital')}
              iconType="icon-sqlexecutions"
              value={sqlExecuteData?.executeSqlTotalCount || 0}
              subValue={Number(sqlExecuteData?.todayTotalSequentialGrowth || 0)}
              onClick={() => onGoAuditMenu('total') }
            />
          </Col>
          <Col span={8}>
            <StatisticItem
              isCompare
              title={t('home.overview.executeFail')}
              iconType="icon-executionfailures"
              value={sqlExecuteFailData?.executeFailSqlTotalCount || 0}
              subValue={Number(sqlExecuteFailData?.todayFailSequentialGrowth || 0)}
              onClick={() => onGoAuditMenu('error') }
            />
          </Col>
        </Row>
      </div>
    </div>
  )
}