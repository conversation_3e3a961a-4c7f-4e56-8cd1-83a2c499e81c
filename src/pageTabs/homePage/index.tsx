import React, { memo } from "react";
import classNames from 'classnames';

import ErrorBoundary from 'antd/lib/alert/ErrorBoundary'
import { Layout, Alert, Row, Col } from 'antd'
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from "src/hook";
import { Iconfont } from "src/components";
import homeGuideIcon from 'src/assets/img/homeGuideIcon.png'
import { showModal } from 'src/store/extraSlice/modalVisibleSlice'
import OverviewStatisCard from './OverviewStatisCard';
import OrderStatisCard from './OrderStatisCard';
import MessageCard from "./MessageCard";
import FreqExecuteDbTop5Card from './FreqExecuteDbTop5Card';
import LongestTimeSqlCard from './LongestTimeSqlCard';
import SqlExecuteCountCard from './sqlExecuteCountCard';
import styles from './index.module.scss'

export default memo(() => {

  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { userName } = useSelector(state => state.login.userInfo);

  return (
    <Layout className={classNames("cq-container", [styles.homeIndexWrapper])}>
      <Alert
        banner
        className={styles.homeAlert}
        type='info'
        icon={<Iconfont type="icon-tishi1" />}
        message={
          <div className="flexAlignCenter"> 
          <img style={{ height: 22, width: 21, marginRight: 8 }} src={homeGuideIcon} />
          {t('home.guide.tip', {userName})}
            <span className="linkStyle ml4" onClick={() => { dispatch(showModal('ModalSceneGuide')) }}>{t('home.guide.entry')}</span>
          </div>
        }
      />
      <div className={styles.homeIndexContent}>
        <div className={styles.indexTop}>
          <ErrorBoundary>
            <Row gutter={[23, 23]} align='stretch'>
              <Col span={16}>
                <Row>
                  <Col span={24}>
                    <OverviewStatisCard />
                  </Col>
                  <Col span={24} className={styles.mt24}>
                    <OrderStatisCard />
                  </Col>
                </Row>
              </Col>
              <Col span={8}>
                <MessageCard />
              </Col>
            </Row>
            <Row gutter={[23, 23]} align='stretch'>
              <Col span={12}>
                <FreqExecuteDbTop5Card />
              </Col>
              <Col span={12}>
                <LongestTimeSqlCard />
              </Col>
            </Row>
            <Row>
              <Col span={24}>
                <SqlExecuteCountCard />
              </Col>
            </Row>
          </ErrorBoundary>
        </div>
      </div>
    </Layout>
  )
})