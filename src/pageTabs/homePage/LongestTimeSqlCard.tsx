import React, { useEffect } from "react";
import classNames from "classnames";
import { Table, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { useHistory } from "react-router-dom";
import { useRequest, useDispatch, useSelector, useConditionalPolling } from 'src/hook';
import { getHomeSqlCostTop5, ISQLCostItem } from 'src/api';
import { setUserExecuteLogHistoryParams } from 'src/pageTabs/audit/overview/overviewSlice'

import styles from "./index.module.scss";

export default () => {

  const { t } = useTranslation();
  const history = useHistory();
  const dispatch = useDispatch();
  const { activeKey } = useSelector(state => state.pageTabs);

  const { data, loading, cancel, refresh, run: runGetHomeSqlCostTop5 } = useRequest(getHomeSqlCostTop5, { manual: true })

  useEffect(() => {
    runGetHomeSqlCostTop5()
  }, [])

  //当前页面激活时刷新数据
  useConditionalPolling({
    shouldPoll: (key) => key === '/system_home',
    pollFn: refresh,
    cancelFn: cancel,
    deps: [activeKey],
    interval: 1000 * 60
  });
  const onHandleToUserLogMenu = (id?: number) => {
    history.push('/sql-log');
    dispatch(setUserExecuteLogHistoryParams({ id }))
  }

  const columns = [
    {
      dataIndex: 'originSql',
      title: t('home.costSql.table.originSql'),
      render: (value: string, record: ISQLCostItem) => {
        return <Tooltip title={value}>
          <div className={styles.linkTo} onClick={() => { onHandleToUserLogMenu(record?.id) }}>
            {value?.replace(/^([\s\S]{41})[\s\S]*/, '$1...')}
          </div>
        </Tooltip>
      }
    },
    {
      dataIndex: 'sqlType',
      title: t('home.costSql.table.sqlType'),
    },
    {
      dataIndex: 'executeCostMs',
      title: t('home.costSql.table.executeCostMs'),
      render: (value: string) => `${Number(value).toLocaleString()}ms`
    }
  ]

  return (
    <div className={classNames(styles.commonCardStyle, styles.sqlLongestTimeTop5Card)}>
      <div className={styles.title}>{t('home.costSql.title')}</div>
      <div className={styles.container}>
        <Table
          loading={loading}
          size='small'
          columns={columns}
          dataSource={data}
          pagination={false}
        />
      </div>
    </div>

  )
}