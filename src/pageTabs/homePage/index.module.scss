.homeIndexWrapper {
  height: calc(100vh - 48px - 30px);
  padding: 0 18px 24px 24px;
  background-color: #f5f7fb;

  .homeAlert {
    margin: 20px 10px 20px 0;
    padding-left: 16px;
    display: flex;
    align-items: center;
    background-color: #E8F3FF;

    .alertMes {
      margin-top: 1px;
      display: inline-block;
    }
  }

  .title {
    font-size: 18px;
    font-weight: 500;
  }
  .mt24 {
    margin-top: 24px;
  }

  .linkTo {
    cursor: pointer;

    &:hover {
      color: #165dff !important;
    }
  }

  .homeIndexContent {
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    padding-right: 6px;
  }

  .commonCardStyle {
    height: 100%;
    background-color: #ffffff;
    padding: 16px 0px 8px 24px;
    box-shadow: 0px 4px 16px 0px rgba(219, 220, 226, 0.25);
    border-radius: 8px;
  }

  .overviewStatisCard {
    background-color: #ffffff;
    padding: 16px 0px 8px 24px;
    box-shadow: 0px 4px 16px 0px rgba(219, 220, 226, 0.25);
    border-radius: 8px;

    .content {
      display: flex;
      align-items: center;

      .row {
        width: 100%;

        .statiscItem {

          .itemContainer {
            padding: 16px 20px;
            border-radius: 4px;

          }

          .statiscIcon {
            font-size: 48px;
            margin-right: 9px;
          }

          .statiscTitle {
            font-family: regular;
            color: #1D2129;
            font-weight: 300;
            margin-bottom: 10px;
          }

          .statisicValue {
            .value {
              color: #1D2129;
              font-size: 20px;
              font-weight: 700;
            }

            .desc {
              font-size: 12px;
              color: #878B91;
            }
          }
        }

        .itemHover {

          :hover {
            cursor: pointer;
            background-color: #F5F7FA;
          }
        }
      }
    }
  }

  .orderStatisCard {
    .title {
      margin-bottom: 24px !important;
    }

    .header {
      padding-right: 21px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .itemContainer {
      padding: 0 0 0 10px !important;
    }

    .orderTitle {
      color: #0F244C;
      font-size: 20px !important;
      font-weight: 900 !important;
    }

    .orderValue {
      color: #6C7293;
      font-size: 14px !important;
    }
  }

  .messageCard {
    padding-right: 20px;

    .container {
      overflow-y: auto;
      height: 294px;
      padding-right: 4px;
    }

    .content {
      color: #0F244C;
      position: relative;
      width: 100%;
      line-height: 22px;
      margin-bottom: 14px;
      display: flex;
      /* 启用Flex布局 */
      flex-wrap: wrap;
      /* 允许换行 */
      align-items: baseline;

      /* 时间与文本基线对齐 */
      .contentText {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        /* 限制两行 */
        -webkit-box-orient: vertical;
        overflow: hidden;
        min-height: 44px;
        /* 两行高度 = 2 * 1.5em */
        word-break: break-word;
        /* 处理长单词/中文换行 */
      }

      .extra {
        position: absolute;
        margin-top: 21px;
        right: 0;

        /* 时间不换行 */
        .time {
          font-size: 12px;
          color: #878B91;
        }

        .msgType {
          color: #165DFF;
          margin-left: 12px;
          font-size: 12px;
          padding: 4px 8px;
          border-radius: 2px;
          background-color: #E8F3FF;
        }
      }
    }
  }

  .freqExecuteDbTop5Card {
    .container {
      padding: 16px 32px 22px 0;
    }
  }

  .sqlLongestTimeTop5Card {
    .container {
      padding: 16px 32px 22px 0;
    }
  }

  .sqlExecuteCountCard {
    padding-bottom: 20px;
    padding-right: 20px;

    :global {
      .rc-segmented {

        .rc-segmented-item {
          min-width: auto;
        }
      }

    }

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;

    }

    .typeOpetion {
      margin-bottom: 16px;
      display: inline-block;
    }
  }
}