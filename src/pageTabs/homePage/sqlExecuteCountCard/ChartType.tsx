import React, { useEffect } from 'react';
import { Chart } from '@antv/g2';
import {  } from 'src/components';
import { EmptyChart } from 'src/pageTabs/audit/overview/Charts';
import { IChartType } from './index';
import { useTranslation } from 'react-i18next';

export default ({
  data,
  chartType
}: {
  data?: any[];
  chartType: IChartType
}) => {

  const { t } = useTranslation();
  const renderSqlCountChart = (container: string, data: any[]) => {
  
    const chart = new Chart({
      container,
      autoFit: true,
      height: 160,
    })
   
    chart.data(data)
    chart.axis('executionDate', {
      tickLine: {
        alignTick: false,
      },
      label: {
        style: {
          maxWidth: 10
        }
      }
    });

    chart.scale('executeCount', {
      formatter: (v) => (v || '0')+ t('home.sqlExecute.chart.xAxis') ,
      nice: true,

    });


    chart.interval().position('executionDate*executeCount');
    chart.interaction('element-active');
    chart.render()
    return chart
  }
  
  useEffect(() => {

    if (!data || data.length <= 0) return
 
    const chart = renderSqlCountChart('home-sql-connection', data)
    return () => chart.destroy()
  }, [JSON.stringify(data), chartType, t])


  return (
    data?.length ? 
    <div id="home-sql-connection" ></div>
    : <EmptyChart/>
  )
}

