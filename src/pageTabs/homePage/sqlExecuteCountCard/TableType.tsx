import React, { useMemo } from "react";
import { Table } from 'antd';
import { IDatasourceExecuteCountItem } from 'src/api';
import { useTranslation } from "react-i18next";

export default ({
  loading = false,
  data,
  databaseTypes = []
}: {
  loading: boolean;
  data: any;
  databaseTypes?: string[]
}) => {

  const { t } = useTranslation();

  const columns = [
    {
      dataIndex: 'executionDate',
      title: t('home.sqlExecute.table.time'),
    },
    {
      dataIndex: 'executeCount',
      title: t('home.sqlExecute.table.total'),
    }
  ]

  const newColumns = useMemo(() => {
    if (!databaseTypes?.length) return columns;
    const filteredColumns = databaseTypes.map(type => ({
      dataIndex: type,
      title: type + t('home.sqlExecute.table.count'),
      render: (val: string,record: any) => {
        const curTypeItem = record?.datasourceExecuteCount?.find((item: IDatasourceExecuteCountItem) => item?.logType === type.toUpperCase())
        return curTypeItem?.executeCount || 0;
      },
    }))

    return columns.concat(filteredColumns);
  }, [JSON.stringify(databaseTypes), t])

  return (
    <Table
      loading={loading}
      size="small"
      columns={newColumns}
      dataSource={data}
      pagination={false}
      scroll={{ x: databaseTypes?.length > 3 ? 1600 : 'auto', y: 500 }}
    />
  )
}