import React, { useState, useEffect } from "react";
import classNames from "classnames";
import * as _ from 'lodash';
import {  DatePicker, Select } from 'antd';
import dayjs from 'dayjs'
import { useRequest, useSelector, useConditionalPolling } from 'src/hook';
import { getSupportedConnectionTypes, getHomeSqlExecuteCount } from 'src/api';
import { useTranslation } from "react-i18next";
import styles from "../index.module.scss";
import { Iconfont, RcSegmented } from "src/components";
import ChartType from './ChartType';
import TableType from "./TableType";

export type IChartType = 'CHART' | 'TABLE';

export default () => {

  const { t } = useTranslation();
  const weekStart = dayjs().startOf('week');
  const weekEnd = dayjs().endOf('week').startOf('day');
  //本月
  const monthStart = dayjs().startOf('month');
  const monthEnd = dayjs().endOf('month').startOf('day');

  const CHART_TYPES = ['CHART', 'TABLE'];
  const DATE_TYPES = [t('home.sqlExecute.date.tab1'), t('home.sqlExecute.date.tab2')]

  const { activeKey } = useSelector(state => state.pageTabs);
  const [chartType, setChartType] = useState<IChartType>('CHART');
  const [dateType, setDateType] = useState<string>(DATE_TYPES[0]);
  const [databaseTypes, setDatabaseTypes] = useState<string[]>([])
  const [rangePickerTimes, setRangePickerTimes] = useState<any[]>([weekStart, weekEnd])

  const { data: connectionTypes = [] } = useRequest(getSupportedConnectionTypes,
    {
      formatResult: (data) => {
        return data?.map((dataSource) => ({
          label: <><Iconfont type={`icon-${dataSource?.dataSourceName}`} className="mr10" />{dataSource?.dataSourceName}</>,
          value: dataSource?.dataSourceName,
        }))
      },
    },
  )
  //sql数据
  const { data: sqlExecuteData = [], loading: dataSourceLoading, cancel, refresh ,run: runGetHomeSqlExecuteCount } = useRequest(getHomeSqlExecuteCount, { 
    manual: true,
    formatResult: (data) => {
       const sortedData  = data?.sort((a, b) => {
        const aDate = new Date(a.executionDate ?? 0).getTime();
        const bDate = new Date(b.executionDate ?? 0).getTime();
        return aDate - bDate;
      });
       return sortedData
    }
   });

  useEffect(() => {
    runGetHomeSqlExecuteCount({
      databaseTypes,
      beginTimeMs: dayjs(rangePickerTimes[0]).startOf('d').format('x'),
      endTimeMs: dayjs(rangePickerTimes[1]).endOf('d').format('x'),
    })
  }, [databaseTypes, rangePickerTimes])

  //当前页面激活时刷新数据
  useConditionalPolling({
    shouldPoll: (key) => key === '/system_home',
    pollFn: refresh,
    cancelFn: cancel,
    deps: [activeKey],
    interval: 1000*60
  });

  return (
    <div className={classNames(styles.commonCardStyle, styles.sqlExecuteCountCard)}>
      <div className={styles.header}>
        <div className={styles.title}>{t('home.sqlExecute.title')}</div>
          <RcSegmented
            value={chartType}
            options={CHART_TYPES.map(type => ({ label: <Iconfont type={type === 'CHART' ? "icon-view2" : 'icon-table1'} />, value: type }))}
            onChange={(val) => {
              setChartType(val as IChartType);
            }}
          />
      </div>
      <div className={classNames(styles.typeOpetion, 'mt10') }>
        <RcSegmented
          value={dateType}
          options={DATE_TYPES}
          onChange={(val) => {
            setDateType(val as string);
            setRangePickerTimes(val === DATE_TYPES[0] ? [weekStart, weekEnd] : [monthStart, monthEnd])
          }}
        />
        <DatePicker.RangePicker
          className="ml10"
          //@ts-ignore
          value={rangePickerTimes}
          onChange={(dates, dateStrings) => {
            //未设置值 使用默认本周
            if (!dates) {
              setDateType(DATE_TYPES[0]);
              setRangePickerTimes([weekStart, weekEnd]);
              return
            }

            setRangePickerTimes(dates);
            //回显 设置值是否为本周/本月 

            if (dayjs(dateStrings[0]).valueOf() === weekStart.valueOf() && dayjs(dateStrings[1]).valueOf() === weekEnd.valueOf()) {
              setDateType(DATE_TYPES[0]);
            } else if (dayjs(dateStrings[0]).valueOf() === monthStart.valueOf() && dayjs(dateStrings[1]).valueOf() === monthEnd.valueOf()) {
              setDateType(DATE_TYPES[1]);
            }

          }}
          getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
        />
        <Select
          showSearch
          mode="multiple"
          showArrow
          allowClear
          maxTagCount={1}
          placeholder={t('home.sqlExecute.dbSelect.plac')}
          options={connectionTypes}
          style={{ marginLeft: 10, width: 180 }}
          onChange={(values: string[]) => setDatabaseTypes(values)}
          getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
        />
      </div>
      <div className={styles.container}>
        {
          chartType === 'CHART' ?
            <ChartType data={sqlExecuteData} chartType={chartType}/>
            : 
            <TableType databaseTypes={databaseTypes} loading={dataSourceLoading} data={sqlExecuteData} />
        }
      </div>
    </div>

  )
}