import React, { useEffect } from "react";
import classNames from "classnames";
import { Table, Tooltip } from 'antd';
import { useHistory } from "react-router-dom";
import { useRequest, useDispatch, useSelector, useConditionalPolling } from 'src/hook';
import { getHomeSqlOperationTop5, ISqlOperationTop5Item } from 'src/api';
import { Iconfont } from "src/components";
import { setActiveKey, addPageTabPanes } from 'src/appPages/main/pageTabs/pageTabsSlice';
import { addPane, updatePaneInfo } from "src/pageTabs/queryPage/queryTabs/queryTabsSlice";
import styles from "./index.module.scss";
import { useTranslation } from "react-i18next";

export default () => {

  const history = useHistory();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { activeKey } = useSelector(state => state.pageTabs);
  const { pageTabPanes } = useSelector(state => state.pageTabs);
  const { tabKeyList } = useSelector(state => state.queryTabs);
  const { permissionMenus = [] } = useSelector(state => state.login.userInfo)
 //判断有无数据操作菜单
 const hasDataOperateMenu = permissionMenus.some(item => item?.menuType === 'SYSTEM_DATA_OPERATE');

  const { data = [], loading, cancel, refresh, run: runGetHomeSqlOperationTop5 } = useRequest(getHomeSqlOperationTop5, { manual: true })

  
  useEffect(() => {
    runGetHomeSqlOperationTop5()
  }, [])

  //当前页面激活时刷新数据
  useConditionalPolling({
    shouldPoll: (key) => key === '/system_home',
    pollFn: refresh,
    cancelFn: cancel,
    deps: [activeKey],
    interval: 1000*60
  });

   // 创建或者更新pane
   const handleDataOperatePane = (record: any) => {
   
    const { schemaName, dbName, completeNameToView, datasourceType, connectionId } = record;
    const key = completeNameToView;
   
    const paneInfo = {
      connectionId,
      connectionType: datasourceType,
      databaseName: ['DamengDB', 'DB2'].includes(datasourceType as any) ? schemaName : dbName || schemaName, //两层数据源可能
      schemaName,
      isHome: true, // 作为数据变更跳转到数据操作的标识字段
      schemaDisabled: !!schemaName, // schema下拉框的禁用状态
    }
    if (tabKeyList.includes(key)) {
      dispatch(
        updatePaneInfo({
          key,
          paneInfo
        })
      )
    }
    else {
      dispatch(
        addPane({
          ...paneInfo,
          tabName: dbName || schemaName,
          key,
          plSql: false,
          tSql: false,
          autoRun: false,
          paneType: 'monaco',
        }),
      )
    }
  }

  const onHandleToUserLogMenu = (record: any) => {
    handleDataOperatePane(record);
    history.push('/system_data_operate');
    if(pageTabPanes.includes('/system_data_operate')){
      dispatch(setActiveKey('/system_data_operate'));
    }
    else {
      dispatch(addPageTabPanes('/system_data_operate'));
    }
  }

  const columns = [
    {
      dataIndex: 'completeNameToView',
      title:t('home.freqSql.table.completeNameToView'),
      render: (value: string, record: ISqlOperationTop5Item) => {

        return <Tooltip title={value}>
          <div
           //判断是否有菜单在跳转
            className={classNames('flexAlignCenter', { [styles.linkTo]: hasDataOperateMenu })}
            onClick={() => { hasDataOperateMenu && onHandleToUserLogMenu(record) }}>
            <Iconfont type={`icon-${record.datasourceType}`} className="mr10" />
            {value?.replace(/^(.{30}).*/, '$1...')}
          </div>
        </Tooltip>
      }
    },
    {
      dataIndex: 'totalOperations',
      title: t('home.freqSql.table.totalOperations'),
    },
    {
      dataIndex: 'dqlCount',
      title: 'DQL/DML/DDL/DCL/TCL',
      render: (val: number, record: ISqlOperationTop5Item) => {
        const { dqlCount = 0, dmlCount = 0, ddlCount = 0, dclCount = 0, tclCount = 0 } = record;
        return `${dqlCount} / ${dmlCount} / ${ddlCount} / ${dclCount} / ${tclCount}`
      }
    }
  ]

  return (
    <div className={classNames(styles.commonCardStyle, styles.freqExecuteDbTop5Card)}>
      <div className={styles.title}>{t('home.freqSql.title')}</div>
      <div className={styles.container}>
        <Table
          loading={loading}
          size="small"
          columns={columns}
          dataSource={data}
          pagination={false}
        />
      </div>
    </div>

  )
}