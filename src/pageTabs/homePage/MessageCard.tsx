import React, { useEffect } from "react";
import classNames from "classnames";
import { <PERSON>, <PERSON>, Tooltip, Skeleton } from 'antd';
import { useDispatch, useSelector, useRequest, useConditionalPolling } from "src/hook";
import { useHistory } from 'react-router-dom';
import {
  putReadMes,
  getAllUnReadMes
} from 'src/api'
import {
  setMineApplyPageState,
  setMineApplyDetailParams,
  setMineApprovePageState,
  setMineApproveDetailParams
} from 'src/pageTabs/access-request/accessRequestSlice'
import {
  setDataChangeMineApplyPageState,
  setDataChangeMineApplyDetailParams,
  setDataChangeMineApprovePageState,
  setDataChangeMineApproveDetailParams,
} from 'src/pageTabs/data-change/dataChangeSlice'
import {
  setOverviewPageState,
  setOverviewPageDetailParams
} from 'src/pageTabs/audit/overview/overviewSlice'
import { showModal } from 'src/store/extraSlice/modalVisibleSlice'
import { setUnauthorizedOperationState } from 'src/pageTabs/audit/auditSlice';
import { setTabsKey, setExportParamsFromMessage } from 'src/pageTabs/taskCenter/taskCenterSlice'
import { ProblemConnectionModal } from 'src/pageTabs/userMesPage/components/ProblemConnectionModal'
import { MESSAGE_TYPE_MAPPING } from 'src/pageTabs/userMesPage/constants'
import styles from './index.module.scss';
import { useTranslation } from "react-i18next";
import { EmptyChart } from "../audit/overview/Charts";

export default () => {

  const { t } = useTranslation();
  //支持跳转的消息类型
  const withLinkMsgSourceTypes = ['PROCESS', 'RESULTSET_EXPORT', 'AUDIT_EXPORT', 'USER_EXPORT'];
  const withLinkAlarmType = ['PROBLEM_CONNECTION', 'SQL_CHECK', 'OVER_PERMISSION', 'SLOW_SQL', 'HIGH_RISK', 'EXPORT_FILE_SIZE'];

  const history = useHistory();
  const dispatch = useDispatch();
  const { activeKey } = useSelector(state => state.pageTabs);

  const { data: msgData, loading, cancel, refresh, run: runGetAllUnReadMes } = useRequest(getAllUnReadMes, {
    manual: true,
    formatResult: (res) => {
      return res?.data?.map((item: any) => {
        const { notificationVoExtra = {}, extraAttrsJson = {}, businessId, category, senderId, receiverId } = item
        return ({
          ...item,
          ...notificationVoExtra,
          ...extraAttrsJson,
          alarmType: extraAttrsJson?.alarmType,
          param: extraAttrsJson?.alarmParam || {},
          applyId: businessId,
          msgId: businessId,
          msgType: category,
          sender: senderId,
          flowTaskId: extraAttrsJson?.taskId,
          userId: receiverId ? [receiverId] : []
        })
      })
    }
  })

  useEffect(() => {
    runGetAllUnReadMes({ pageSize: 10, pageNum: 1 })
  }, [])

  //当前页面激活时刷新数据
  useConditionalPolling({
    shouldPoll: (key) => key === '/system_home',
    pollFn: refresh,
    cancelFn: cancel,
    deps: [activeKey],
    interval: 1000 //1s
  });
  //已读
  const onReadMsg = async (id: number) => {
    await putReadMes(id)
    runGetAllUnReadMes({ pageSize: 10, pageNum: 1 });
  }


  const onHandleTitleClick = (item: any) => {

    if (item.type === "dataCorrection" || item.type === "publishChanges") {
      const params = {
        ...item,
        id: item?.dataChangeId,
        mainUUID: item?.flowMainUUID,
        originType: 'message'
      }
      if (item.applyType === "APPROVAL") {
        history.push('/data_change_mine_approve')
        dispatch(setDataChangeMineApprovePageState('detail'))
        dispatch(setDataChangeMineApproveDetailParams(params))
      } else {
        // 数据变更-我的申请-详情
        history.push('/data_change_mine_apply')
        dispatch(setDataChangeMineApplyPageState('detail'))
        dispatch(setDataChangeMineApplyDetailParams(params))
      }

    } else {
      const params = {
        ...item,
        id: item?.flowUUID,
        mainUUID: item?.flowMainUUID,
        originType: 'message'
      }

      if (item.applyType === "APPROVAL") {
        // 流程-我的审批-详情
        history.push('/mine_approve')
        dispatch(setMineApprovePageState('detail'))
        dispatch(setMineApproveDetailParams(params))
      } else {
        // 流程-我的申请-详情
        history.push('/mine_apply')
        dispatch(setMineApplyPageState('detail'))
        dispatch(setMineApplyDetailParams(params))
      }
    }
  }

  // 渲染语句明细
  const gotoStatementDetail = (params: any) => {
    dispatch(setOverviewPageState('statement_detail'))
    dispatch(setOverviewPageDetailParams(params))
  }

  // 渲染越权操作
  const gotoOperateUnauthorized = (params: any) => {
    dispatch(setOverviewPageState('operate_unauthorized'))
    dispatch(setOverviewPageDetailParams(params))
  }
  const onHandleJumpToAuditAnalysis = async (item: any) => {

    let params: any = {
      timeRange: undefined,
      auditIds: item?.param?.['${SYSTEM.audit_ids}'] ? item.param['${SYSTEM.audit_ids}'].split(',') : []
    };

    switch (item?.alarmType) {
      case 'OVER_PERMISSION':
        dispatch(setUnauthorizedOperationState(params));
        // 越权操作
        gotoOperateUnauthorized(params);
        break;
      default:
        // 语句明细
        gotoStatementDetail(params);
        break;
    }

    history.push({
      pathname: '/audit_view',
      state: params
    })
  }

  const onHandleProblemConnectionClick = async (item: any) => {
    dispatch(showModal('ProblemConnectionModal', item));
  }

  const goDownloadMenu = (businessId: number, id?: number,) => {

    history.push('/download');
    dispatch(setTabsKey('handleExport'))
    dispatch(setExportParamsFromMessage({ taskId: businessId, dataPicker: [] }));

  }

  const onHandleToPage = (item: any) => {
    if (withLinkMsgSourceTypes.includes(item?.msgSourceType) || withLinkAlarmType.includes(item?.alarmType)) {

      //如果是未读 再去标记已读
      if (item?.status === 'UNREAD') {
        onReadMsg(item?.id)
      }

      if (item?.msgSourceType === 'PROCESS') {
        onHandleTitleClick(item)
      } else if (['RESULTSET_EXPORT', 'AUDIT_EXPORT', 'USER_EXPORT'].includes(item?.msgSourceType) || ['EXPORT_FILE_SIZE'].includes(item?.alarmType)) {
        goDownloadMenu(item?.businessId)

      } else if (item?.alarmType === 'PROBLEM_CONNECTION') {
        onHandleProblemConnectionClick(item)
      } else {
        onHandleJumpToAuditAnalysis(item)
      }
    }
  }

  return (
    <div className={classNames(styles.commonCardStyle, styles.messageCard)}>
      <div className={styles.title}>{t('home.msg.title')}</div>
      {
        !msgData?.length ?
          <EmptyChart />
          :
          <Skeleton loading={false} paragraph={{ rows: 7 }}>
            <div className={styles.container}>
              <Row gutter={[0, 12]} >
                {
                  msgData?.map((item: any) => {
                    //是否支持跳转
                    const supportLinkTo = withLinkMsgSourceTypes.includes(item?.msgSourceType) || withLinkAlarmType.includes(item?.alarmType);
                    return (
                      <Col span={24} key={item?.id}>
                        <div className={classNames(styles.content, { [styles.linkTo]: supportLinkTo })} onClick={() => onHandleToPage(item)}>
                          <div className={styles.contentText}>
                            <Tooltip title={item?.content}>
                              {item?.content?.replace(/^(.{50}).*/, '$1...')}
                            </Tooltip>
                          </div>
                          <div className={styles.extra}>
                            <span className={styles.time}>{item?.howLongAgo}</span>
                            {/* @ts-ignore */}
                            <span className={styles.msgType}>{MESSAGE_TYPE_MAPPING[item?.msgSourceType]}</span>
                          </div>
                        </div>
                      </Col>
                    )
                  })
                }
              </Row>
            </div>
          </Skeleton>
      }
      <ProblemConnectionModal />
    </div>
  )
}