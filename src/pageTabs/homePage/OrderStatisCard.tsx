import React, { useEffect, useMemo, useState } from "react";
import { Row, Col, Statistic, Select } from 'antd';
import classNames from "classnames";
import { useHistory } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useSelector, useDispatch, useRequest, useConditionalPolling } from "src/hook";
import { RcSegmented } from "src/components";
import {
  setMineApplyPageState,
  setMineApplyDetailParams,
  setMineApprovePageState,
  setMineApproveDetailParams
} from 'src/pageTabs/access-request/accessRequestSlice';
import {
  setDataChangeMineApplyPageState,
  setDataChangeMineApplyDetailParams,
  setDataChangeMineApprovePageState,
  setDataChangeMineApproveDetailParams
} from 'src/pageTabs/data-change/dataChangeSlice';
import { 
  getHomeApplyOrderDetailInfo,
  getHomeApproveOrderDetailInfo
} from 'src/api';
import { v4 as uuid } from 'uuid'
import i18n from "i18next";
import styles from './index.module.scss';


type IActiveTab = 'ALL' | 'UnderReview' | 'Approved' | 'Rejected' | 'Withdrawn';

const FLOW_TYPES = ['dataManipulation', 'highRisk', 'exportTask', 'importTask', 'desensitizedResource', 'dataCorrection']
type IFlowType = typeof FLOW_TYPES[number];
const FLOW_FIELD_MAPPING : {[key in IFlowType]: string} = {
  dataManipulation: i18n.t('home.order.dataManipulation'),
  highRisk: i18n.t('home.order.highRisk'),
  exportTask: i18n.t('home.order.exportTask'),
  importTask: i18n.t('home.order.importTask'),
  desensitizedResource: i18n.t('home.order.desensitizedResource'),
  dataCorrection: i18n.t('home.order.dataCorrection')
}
const StatisticItem = ({
  title,
  value,
  onClick
}: {
  title: number;
  value: string;
  onClick?: any
}) => {

  return (
    <div className={classNames(styles.statiscItem, {
      [styles.itemHover]: onClick ? true : false,
    })} onClick={() => onClick?.()}>
      <div className={styles.itemContainer}>
        <Statistic
          title={<div className={styles.orderTitle}>{title}</div>}
          formatter={() =>
            <div className={styles.orderValue}>{value}</div>
          }
        />
      </div>
    </div>
  )
}

export default () => {

  const history = useHistory();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { activeKey } = useSelector(state => state.pageTabs);
  const { permissionMenus = [] } = useSelector(state => state.login.userInfo);

  const [activeTab, setActiveTab] = useState<IActiveTab>('UnderReview');
  const [flowType, setFlowType] = React.useState<'mine_apply' | 'mine_approve'>('mine_apply')

  //订单信息
  const { data: applyData, loading: applyLoading,refresh, cancel, run: runGetHomeApplyOrderDetailInfo } = useRequest(
    (params) => {
      if (flowType === 'mine_apply') {
        return getHomeApplyOrderDetailInfo(params)
      }
      return getHomeApproveOrderDetailInfo(params)
    }, { manual: true })

  useEffect(() => {
   
      runGetHomeApplyOrderDetailInfo({
        flowTypes: FLOW_TYPES,
        tab: activeTab === 'ALL' ? '': activeTab
      })
    
  },[flowType, activeTab])

   //当前页面激活时刷新数据
   useConditionalPolling({
    shouldPoll: (key) => key === '/system_home',
    pollFn: refresh,
    cancelFn: cancel,
    deps: [activeKey],
    interval: 1000*60
  });

  const hasPointMenuPerm = (pointMenuType: string, parentMenuType: string) => {
    //是否有子菜单
    return permissionMenus.some(obj => obj.menuType === parentMenuType && obj?.items?.some(item => item?.permissionType === pointMenuType))
  }

  //判断有无流程-我的申请| 我的审批 菜单
  const hasPointFlowMenuPerm = useMemo(() => {
    const isApply = flowType === 'mine_apply';
    return hasPointMenuPerm(isApply ? 'MINE_APPLY' : 'MINE_APPROVE', 'FLOW_APPLY')
  }, [flowType])
  //判断有无流程-我的申请||我的审批菜单
  const hasDataChangeMenuPerm = useMemo(() => {
    const isApply = flowType === 'mine_apply';
    //我的审批 没有撤回选项 不跳转
    if (activeTab === 'Withdrawn' && !isApply) return false;
    return hasPointMenuPerm(isApply ? 'DATA_CHANGE_MINE_APPLY' : 'DATA_CHANGE_MINE_APPROVE', 'DATA_CHANGE')
  }, [flowType, activeTab])

  const getFlowMineApplyParams = (filterTypes: string | string[]) => {
    let params: any = {
      curTab:'all',
      priGranType: filterTypes,
      uuid: uuid() //手动切换tab后 从之前入口再次进去tab不会切换问题;
    }

    if (activeTab === 'UnderReview') {
      params.flowApplyStatus = ["pending","power"]
    } else if (activeTab === 'Withdrawn') {
      params.curTab = 'withdraw';
    }else if (activeTab === 'Approved') {
      params.flowApplyStatus = ['pass']
    }else if (activeTab === 'Rejected') {
      params.flowApplyStatus = ['refuse']
    }
    return params;
  }

  const onHandleFlowOrderDetail = (filterTypes: string | string[]) => {
    //跳转到流程申请 定位的tab
    const params = getFlowMineApplyParams(filterTypes);
   
    if (flowType === 'mine_apply') {
      dispatch(setMineApplyPageState(''))
      dispatch(setMineApplyDetailParams(params))
    } else {
     
      dispatch(setMineApprovePageState(''));
      dispatch(setMineApproveDetailParams(params))
    }

    history.push(`/${flowType}`)
  }

  const onHandleDataChangeOrder = () => {
    //跳转到流程申请 定位的tab
   
    let params: any = {
      curTab: t("data_chg:rcsg_opt.all"),
      uuid: uuid() //手动切换tab后 从之前入口再次进去tab不会切换问题;
    }
    
    if (activeTab === 'UnderReview') {
      params.curTab =t("data_chg:rcsg_opt.pending_review");
    }else if (activeTab === 'Approved') {
      params.executeStatus = 'ExecutionSuccessful'
    }else if (activeTab === 'Rejected') {
      params.executeStatus = 'Rejected'
    }else if (activeTab === 'Withdrawn') {
      params.curTab = t("data_chg:rcsg_opt.withdrawn");
    }

    if (flowType === 'mine_apply') {
      history.push('/data_change_mine_apply')
      dispatch(setDataChangeMineApplyPageState(''));
      dispatch(setDataChangeMineApplyDetailParams(params))
    } else {
      history.push('/data_change_mine_approve')
      dispatch(setDataChangeMineApprovePageState(''));
      dispatch(setDataChangeMineApproveDetailParams(params))
    }

  }

  const ORDER_TYPES: Record<string, any> = useMemo(() => {

    return {
      //'数据库操作权限'
      dataManipulation: hasPointFlowMenuPerm ? () => { onHandleFlowOrderDetail(['THIN', 'FAT']) } : null,   //流程 类型
      //'导入权限'
      importTask : hasPointFlowMenuPerm ? () => { onHandleFlowOrderDetail(['importTask']) } : null,//流程 类型
      //'导出权限'
      exportTask: hasPointFlowMenuPerm ? () => { onHandleFlowOrderDetail(['exportTask']) } : null,//流程 类型
      //'数据变更'
      dataCorrection : hasDataChangeMenuPerm ? () => { onHandleDataChangeOrder() } : null,//数据变更 类型
     //'高危操作权限'
      highRisk: hasDataChangeMenuPerm ? () => { onHandleFlowOrderDetail(['highRisk']) } : null,//数据变更 类型
      //'敏感资源访问权限'
      desensitizedResource: hasDataChangeMenuPerm ? () => { onHandleFlowOrderDetail(['desensitizedResource']) } : null,//数据变更 类型
    }
  }, [flowType, activeTab, hasPointFlowMenuPerm, hasDataChangeMenuPerm])

  const ORDER_SELECT_OPTIONS = useMemo(() => {

    const isMyApply = flowType === 'mine_apply';
    const types: Record<string, string> = {
      ALL: t('home.order.status.ALL'),
      UnderReview: isMyApply ?  t('home.order.status.UnderReview') :  t('home.order.status.UnderReview1'),
      Approved: t('home.order.status.Approved'),
      Rejected: t('home.order.status.Rejected'),
      Withdrawn: t('home.order.status.Withdrawn')   //定位已撤回  数据变更为0
    }
    return Object.keys(types).map(type => ({ label: types[type], value: type }))
  }, [flowType])

  return (
    <div className={classNames(styles.overviewStatisCard, styles.orderStatisCard)}>
      <div className={styles.header}>
        <div className={styles.title}>{t('home.order.title')}</div>
        <div>
          <Select
            bordered={false}
            className="mr10"
            value={activeTab}
            options={ORDER_SELECT_OPTIONS}
            onChange={(key) => setActiveTab(key as IActiveTab)}
          />
          <RcSegmented
            options={[{ label: t('home.order.tab.myApply'), value: 'mine_apply' }, { label: t('home.order.tab.approval'), value: 'mine_approve' }]}
            onChange={(type) => setFlowType(type as 'mine_apply' | 'mine_approve')}
          />
        </div>
      </div>

      <div className={styles.content}>
        <Row gutter={[10, 0]} className={styles.row}>
          {
            Object.keys(ORDER_TYPES).map(order => (
              <Col span={4} key={order}>
                <StatisticItem
                  value={FLOW_FIELD_MAPPING[order]}
                  title={applyData?.[order] || 0}
                  {...(applyData?.[order] ? { onClick: ORDER_TYPES[order] } : {})}
                />
              </Col>
            ))
          }
        </Row>
      </div>
    </div>
  )
}