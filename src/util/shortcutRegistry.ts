type ShortcutCallback = (e: KeyboardEvent) => void

interface ShortcutEntry {
  keys: string[]
  callback: ShortcutCallback
  options?: { ignoreInput?: boolean }
}

const registry: Map<string, ShortcutEntry> = new Map()

export const registerShortcut = (
  id: string,
  keys: string[],
  callback: ShortcutCallback,//注册后的回调函数
  options: { ignoreInput?: boolean } = {}
) => {
  registry.set(id, { keys, callback, options })
}

export const unregisterShortcut = (id: string) => {
  registry.delete(id)
}

export const getRegisteredShortcuts = () => {
  return Array.from(registry.values())
}